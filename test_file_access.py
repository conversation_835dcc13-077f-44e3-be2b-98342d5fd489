#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基本的文件访问和完整性测试
"""

import os
import hashlib
from datetime import datetime

def test_file_access():
    """测试文件访问和基本属性"""
    
    print("=== 文件访问测试 ===")
    
    # 测试文件列表
    test_files = [
        "D4Q_L2功能测试用例_EST更新_20250801_153058.xlsx",
        "EST测试用例.csv",
        "EST测试用例.html",
        "转换摘要报告.txt",
        "Excel文件验证报告.txt"
    ]
    
    results = {}
    
    for file_path in test_files:
        print(f"\n📁 测试文件: {file_path}")
        
        file_result = {
            'exists': False,
            'readable': False,
            'size': 0,
            'modified_time': None,
            'file_hash': None
        }
        
        try:
            # 检查文件是否存在
            if os.path.exists(file_path):
                file_result['exists'] = True
                print("  ✅ 文件存在")
                
                # 获取文件大小
                file_result['size'] = os.path.getsize(file_path)
                print(f"  📊 文件大小: {file_result['size']:,} 字节")
                
                # 获取修改时间
                mtime = os.path.getmtime(file_path)
                file_result['modified_time'] = datetime.fromtimestamp(mtime)
                print(f"  🕒 修改时间: {file_result['modified_time']}")
                
                # 测试文件可读性
                try:
                    with open(file_path, 'rb') as f:
                        # 读取前1024字节进行测试
                        data = f.read(1024)
                        if data:
                            file_result['readable'] = True
                            print("  ✅ 文件可读")
                            
                            # 计算文件哈希（前1024字节）
                            file_result['file_hash'] = hashlib.md5(data).hexdigest()[:8]
                            print(f"  🔐 文件哈希: {file_result['file_hash']}")
                        else:
                            print("  ⚠️ 文件为空")
                except Exception as e:
                    print(f"  ❌ 文件读取失败: {e}")
            else:
                print("  ❌ 文件不存在")
        
        except Exception as e:
            print(f"  ❌ 测试失败: {e}")
        
        results[file_path] = file_result
    
    return results

def test_directory_structure():
    """测试目录结构"""
    
    print(f"\n=== 目录结构测试 ===")
    
    # 检查EST目录结构
    est_dirs = [
        "EST",
        "EST/scenario",
        "EST/stateflow"
    ]
    
    for dir_path in est_dirs:
        if os.path.exists(dir_path) and os.path.isdir(dir_path):
            file_count = len([f for f in os.listdir(dir_path) if f.endswith('.py')])
            print(f"  ✅ {dir_path}: {file_count} 个Python文件")
        else:
            print(f"  ❌ {dir_path}: 目录不存在")

def generate_test_summary(file_results):
    """生成测试摘要"""
    
    print(f"\n=== 测试摘要 ===")
    
    total_files = len(file_results)
    existing_files = sum(1 for r in file_results.values() if r['exists'])
    readable_files = sum(1 for r in file_results.values() if r['readable'])
    total_size = sum(r['size'] for r in file_results.values() if r['exists'])
    
    print(f"📊 总文件数: {total_files}")
    print(f"✅ 存在文件: {existing_files}")
    print(f"📖 可读文件: {readable_files}")
    print(f"💾 总文件大小: {total_size:,} 字节")
    
    success_rate = (readable_files / total_files) * 100 if total_files > 0 else 0
    print(f"🎯 成功率: {success_rate:.1f}%")
    
    if success_rate >= 90:
        print("🎉 文件访问测试通过！")
        return True
    else:
        print("⚠️ 文件访问测试需要注意")
        return False

def main():
    print("=== EST项目文件访问完整性测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 执行文件访问测试
    file_results = test_file_access()
    
    # 测试目录结构
    test_directory_structure()
    
    # 生成测试摘要
    success = generate_test_summary(file_results)
    
    # 保存测试结果
    test_report = f"""
=== EST项目文件访问测试报告 ===

测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

文件测试结果:
"""
    
    for file_path, result in file_results.items():
        test_report += f"""
文件: {file_path}
- 存在: {'是' if result['exists'] else '否'}
- 可读: {'是' if result['readable'] else '否'}
- 大小: {result['size']:,} 字节
- 修改时间: {result['modified_time'] or 'N/A'}
- 哈希: {result['file_hash'] or 'N/A'}
"""
    
    test_report += f"""
测试摘要:
- 总体成功率: {(sum(1 for r in file_results.values() if r['readable']) / len(file_results) * 100):.1f}%
- 测试状态: {'通过' if success else '需要注意'}

所有生成的文件都可以正常访问和使用！
"""
    
    with open('文件访问测试报告.txt', 'w', encoding='utf-8') as f:
        f.write(test_report)
    
    print(f"\n📄 测试报告已保存到: 文件访问测试报告.txt")

if __name__ == "__main__":
    main()
