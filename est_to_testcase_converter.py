#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
EST脚本转换为测试用例的转换器
基于分析的EST脚本结构，将其转换为Excel测试用例格式
"""

import os
import re
import json
from pathlib import Path
from typing import List, Dict, Any

class ESTTestCaseConverter:
    def __init__(self):
        self.test_cases = []
        self.est_status_mapping = {
            "0x0": "Off",
            "0x1": "Passive", 
            "0x2": "Active Warning",
            "0x3": "Active Stop",
            "0x4": "Stand Still",
            "0x5": "Active SpeedAdaption",
            "0x6": "Active RightLaneChange",
            "0x7": "Active Deceleration",
            "0x8": "Active EmergenceLaneChange",
            "0x9": "Failure"
        }
    
    def parse_est_script(self, file_path: str) -> Dict[str, Any]:
        """解析单个EST脚本文件"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取测试用例基本信息
        test_case = {
            'file_name': os.path.basename(file_path),
            'function_name': '',
            'feature': '',
            'story': '',
            'steps': [],
            'signals': [],
            'expected_states': []
        }
        
        # 提取函数名
        func_match = re.search(r'def (test_\w+)', content)
        if func_match:
            test_case['function_name'] = func_match.group(1)
        
        # 提取allure装饰器信息
        feature_match = re.search(r'@allure\.feature\("([^"]+)"\)', content)
        if feature_match:
            test_case['feature'] = feature_match.group(1)
            
        story_match = re.search(r'@allure\.story\("([^"]+)"\)', content)
        if story_match:
            test_case['story'] = story_match.group(1)
        
        # 提取测试步骤
        steps = re.findall(r'with allure\.step\(\s*"""(.*?)"""\s*\):', content, re.DOTALL)
        for step in steps:
            clean_step = re.sub(r'\s+', ' ', step.strip())
            test_case['steps'].append(clean_step)
        
        # 提取信号列表
        signals_match = re.search(r'signals=\[(.*?)\]', content, re.DOTALL)
        if signals_match:
            signals_text = signals_match.group(1)
            signals = re.findall(r'"([^"]+)"', signals_text)
            test_case['signals'] = signals
        
        # 提取期望状态
        state_matches = re.findall(r'target_value=(\d+)', content)
        for state_val in state_matches:
            if f"0x{int(state_val)}" in self.est_status_mapping:
                test_case['expected_states'].append(self.est_status_mapping[f"0x{int(state_val)}"])
        
        return test_case
    
    def scan_est_directory(self, est_dir: str):
        """扫描EST目录，解析所有脚本文件"""
        est_path = Path(est_dir)
        
        # 扫描scenario目录
        scenario_dir = est_path / "scenario"
        if scenario_dir.exists():
            for py_file in scenario_dir.glob("*.py"):
                test_case = self.parse_est_script(str(py_file))
                test_case['category'] = 'scenario'
                self.test_cases.append(test_case)
        
        # 扫描stateflow目录
        stateflow_dir = est_path / "stateflow"
        if stateflow_dir.exists():
            for subdir in stateflow_dir.iterdir():
                if subdir.is_dir():
                    for py_file in subdir.glob("*.py"):
                        test_case = self.parse_est_script(str(py_file))
                        test_case['category'] = f'stateflow/{subdir.name}'
                        self.test_cases.append(test_case)
    
    def generate_test_case_data(self) -> List[Dict[str, Any]]:
        """生成测试用例数据，适合写入Excel"""
        excel_data = []
        
        for i, tc in enumerate(self.test_cases, 1):
            # 基本信息
            test_case_row = {
                '序号': i,
                '测试用例编号': tc['function_name'],
                '测试用例名称': tc['story'] or tc['function_name'],
                '功能模块': tc['feature'],
                '测试类型': tc['category'],
                '文件名': tc['file_name'],
                '前置条件': '',
                '测试步骤': '',
                '预期结果': '',
                '测试数据': '',
                '备注': ''
            }
            
            # 组装测试步骤
            if tc['steps']:
                test_case_row['测试步骤'] = '\n'.join([f"{j+1}. {step}" for j, step in enumerate(tc['steps'])])
            
            # 组装预期结果
            if tc['expected_states']:
                test_case_row['预期结果'] = f"EST状态转换: {' -> '.join(tc['expected_states'])}"
            
            # 测试数据
            if tc['signals']:
                test_case_row['测试数据'] = f"监控信号: {', '.join(tc['signals'])}"
            
            # 前置条件（从第一个步骤推断）
            if tc['steps']:
                first_step = tc['steps'][0]
                if 'HIL台架初始化' in first_step:
                    test_case_row['前置条件'] = 'HIL台架初始化且无故障情况'
            
            excel_data.append(test_case_row)
        
        return excel_data
    
    def save_to_json(self, output_file: str):
        """保存解析结果到JSON文件"""
        excel_data = self.generate_test_case_data()
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(excel_data, f, ensure_ascii=False, indent=2)
        print(f"测试用例数据已保存到: {output_file}")
    
    def print_summary(self):
        """打印转换摘要"""
        print(f"\n=== EST脚本转换摘要 ===")
        print(f"总共解析了 {len(self.test_cases)} 个测试脚本")
        
        # 按类别统计
        categories = {}
        for tc in self.test_cases:
            cat = tc['category']
            categories[cat] = categories.get(cat, 0) + 1
        
        print(f"\n按类别统计:")
        for cat, count in categories.items():
            print(f"  {cat}: {count} 个")
        
        # 显示前几个测试用例示例
        print(f"\n前5个测试用例示例:")
        for i, tc in enumerate(self.test_cases[:5]):
            print(f"  {i+1}. {tc['function_name']} - {tc['story']}")

def main():
    converter = ESTTestCaseConverter()
    
    # 扫描EST目录
    est_directory = "EST"
    if not os.path.exists(est_directory):
        print(f"EST目录不存在: {est_directory}")
        return
    
    print("开始扫描EST脚本...")
    converter.scan_est_directory(est_directory)
    
    # 打印摘要
    converter.print_summary()
    
    # 保存到JSON文件
    converter.save_to_json("est_test_cases.json")
    
    print(f"\n转换完成！生成的测试用例数据可以导入到Excel中。")

if __name__ == "__main__":
    main()
