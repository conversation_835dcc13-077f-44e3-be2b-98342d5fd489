#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将正确格式的CSV转换为Excel，并写入到原始模板中
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Alignment, Font, Border, Side
import os
from datetime import datetime

def convert_csv_to_excel_correct():
    """将正确格式的CSV转换为Excel"""
    
    # 读取正确格式的CSV
    csv_path = r"d:\测试项目\D4Q\测试用例\test_case_generate\EST测试用例_正确格式.csv"
    template_path = r"d:\测试项目\D4Q\测试用例\test_case_generate\D4Q_L2功能测试用例_20250725.xlsx"
    
    # 读取CSV数据
    df = pd.read_csv(csv_path, encoding='utf-8-sig')
    print(f"读取到 {len(df)} 个测试用例")
    
    # 打开Excel模板
    wb = openpyxl.load_workbook(template_path)
    
    # 创建或获取EST工作表
    if 'EST' in wb.sheetnames:
        ws = wb['EST']
        # 清除现有数据（保留表头）
        for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
            for cell in row:
                cell.value = None
    else:
        ws = wb.create_sheet('EST')
        # 创建表头
        headers = ['系统编号', '测试用例编号', '测试用例名称', '功能模块', '测试类型', 
                  '前置条件', '测试步骤和期望结果', '测试数据', '备注']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
    
    # 设置样式
    header_font = Font(bold=True)
    alignment = Alignment(wrap_text=True, vertical='top')
    border = Border(
        left=Side(style='thin'),
        right=Side(style='thin'),
        top=Side(style='thin'),
        bottom=Side(style='thin')
    )
    
    # 应用表头样式
    for col in range(1, 10):
        cell = ws.cell(row=1, column=col)
        cell.font = header_font
        cell.alignment = alignment
        cell.border = border
    
    # 写入数据
    for idx, row in df.iterrows():
        excel_row = idx + 2  # Excel行号从2开始（第1行是表头）
        
        # 系统编号
        ws.cell(row=excel_row, column=1, value=f"SysReq_EST_Func_{idx+1:04d}")
        
        # 测试用例编号
        ws.cell(row=excel_row, column=2, value=row['测试用例名称'])
        
        # 测试用例名称
        test_name = f"EST功能测试_{row['测试场景']}" if row['测试场景'] else f"EST功能测试_{idx+1}"
        ws.cell(row=excel_row, column=3, value=test_name)
        
        # 功能模块
        ws.cell(row=excel_row, column=4, value=row['功能模块'])
        
        # 测试类型
        ws.cell(row=excel_row, column=5, value="功能测试")
        
        # 前置条件
        ws.cell(row=excel_row, column=6, value="HIL台架正常，系统无故障")
        
        # 测试步骤和期望结果
        ws.cell(row=excel_row, column=7, value=row['测试步骤和期望结果'])
        
        # 测试数据
        ws.cell(row=excel_row, column=8, value="")
        
        # 备注
        ws.cell(row=excel_row, column=9, value=f"来源文件: {row['测试用例名称']}")
        
        # 应用样式
        for col in range(1, 10):
            cell = ws.cell(row=excel_row, column=col)
            cell.alignment = alignment
            cell.border = border
    
    # 调整列宽
    column_widths = [15, 25, 30, 15, 12, 20, 80, 15, 25]
    for col, width in enumerate(column_widths, 1):
        ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
    
    # 保存文件
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_path = f"d:\\测试项目\\D4Q\\测试用例\\test_case_generate\\D4Q_L2功能测试用例_EST更新_正确格式_{timestamp}.xlsx"
    wb.save(output_path)
    
    print(f"Excel文件已保存: {output_path}")
    print(f"成功转换 {len(df)} 个测试用例到Excel格式")
    
    return output_path

def validate_excel_format(excel_path):
    """验证Excel格式是否正确"""
    wb = openpyxl.load_workbook(excel_path)
    ws = wb['EST']
    
    print("\n=== Excel格式验证 ===")
    print(f"工作表名称: {ws.title}")
    print(f"数据行数: {ws.max_row - 1}")  # 减去表头行
    print(f"数据列数: {ws.max_column}")
    
    # 检查前几行数据
    print("\n前3行数据预览:")
    for row in range(2, min(5, ws.max_row + 1)):
        test_case = ws.cell(row=row, column=2).value
        steps = ws.cell(row=row, column=7).value
        if steps:
            steps_preview = steps[:100] + "..." if len(steps) > 100 else steps
            print(f"行{row}: {test_case}")
            print(f"  步骤预览: {steps_preview}")
        print()
    
    wb.close()

if __name__ == "__main__":
    try:
        # 转换CSV到Excel
        excel_path = convert_csv_to_excel_correct()
        
        # 验证结果
        validate_excel_format(excel_path)
        
        print("\n✅ 正确格式转换完成！")
        
    except Exception as e:
        print(f"❌ 转换过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
