#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终项目验证脚本
确保所有项目交付物都完整且可用
"""

import os
import hashlib
from datetime import datetime

def validate_project_deliverables():
    """验证项目交付物"""
    
    print("=== EST脚本转换测试用例项目最终验证 ===")
    print(f"验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 核心交付物清单
    core_deliverables = {
        "主要成果文件": [
            "D4Q_L2功能测试用例_EST更新_20250801_153058.xlsx"
        ],
        "数据文件": [
            "EST测试用例.csv",
            "EST测试用例.html"
        ],
        "报告文件": [
            "转换摘要报告.txt",
            "Excel文件验证报告.txt",
            "文件访问测试报告.txt",
            "项目完成文档.md"
        ],
        "工具脚本": [
            "est_to_testcase_converter.py",
            "analyze_template_and_update.py",
            "csv_to_excel.py",
            "validate_excel_output.py",
            "test_file_access.py",
            "final_project_validation.py"
        ]
    }
    
    validation_results = {
        'total_files': 0,
        'existing_files': 0,
        'readable_files': 0,
        'total_size': 0,
        'categories': {},
        'missing_files': [],
        'issues': []
    }
    
    print(f"\n📋 验证项目交付物...")
    
    for category, files in core_deliverables.items():
        print(f"\n📁 {category}:")
        category_stats = {'total': len(files), 'existing': 0, 'readable': 0, 'size': 0}
        
        for file_path in files:
            validation_results['total_files'] += 1
            
            if os.path.exists(file_path):
                validation_results['existing_files'] += 1
                category_stats['existing'] += 1
                
                try:
                    file_size = os.path.getsize(file_path)
                    validation_results['total_size'] += file_size
                    category_stats['size'] += file_size
                    
                    # 测试文件可读性
                    with open(file_path, 'rb') as f:
                        data = f.read(1024)
                        if data:
                            validation_results['readable_files'] += 1
                            category_stats['readable'] += 1
                            print(f"  ✅ {file_path} ({file_size:,} 字节)")
                        else:
                            print(f"  ⚠️ {file_path} (文件为空)")
                            validation_results['issues'].append(f"{file_path}: 文件为空")
                
                except Exception as e:
                    print(f"  ❌ {file_path} (读取失败: {e})")
                    validation_results['issues'].append(f"{file_path}: 读取失败 - {e}")
            else:
                print(f"  ❌ {file_path} (文件不存在)")
                validation_results['missing_files'].append(file_path)
        
        validation_results['categories'][category] = category_stats
        print(f"  📊 {category}: {category_stats['existing']}/{category_stats['total']} 存在, {category_stats['readable']}/{category_stats['total']} 可读")
    
    return validation_results

def validate_est_source_structure():
    """验证EST源文件结构"""
    
    print(f"\n📂 验证EST源文件结构...")
    
    est_structure = {
        "EST": {"type": "dir", "required": True},
        "EST/scenario": {"type": "dir", "required": True},
        "EST/stateflow": {"type": "dir", "required": True}
    }
    
    structure_valid = True
    
    for path, info in est_structure.items():
        if os.path.exists(path):
            if info["type"] == "dir" and os.path.isdir(path):
                if path.endswith("scenario"):
                    py_files = len([f for f in os.listdir(path) if f.endswith('.py')])
                    print(f"  ✅ {path}: {py_files} 个Python文件")
                elif path.endswith("stateflow"):
                    subdirs = [d for d in os.listdir(path) if os.path.isdir(os.path.join(path, d))]
                    total_py = 0
                    for subdir in subdirs:
                        subdir_path = os.path.join(path, subdir)
                        py_count = len([f for f in os.listdir(subdir_path) if f.endswith('.py')])
                        total_py += py_count
                    print(f"  ✅ {path}: {len(subdirs)} 个子目录, {total_py} 个Python文件")
                else:
                    print(f"  ✅ {path}: 目录存在")
            else:
                print(f"  ❌ {path}: 类型不匹配")
                structure_valid = False
        else:
            if info["required"]:
                print(f"  ❌ {path}: 必需目录不存在")
                structure_valid = False
            else:
                print(f"  ⚠️ {path}: 可选目录不存在")
    
    return structure_valid

def generate_final_report(validation_results, structure_valid):
    """生成最终验证报告"""
    
    print(f"\n=== 最终验证报告 ===")
    
    # 计算成功率
    file_success_rate = (validation_results['readable_files'] / validation_results['total_files'] * 100) if validation_results['total_files'] > 0 else 0
    
    print(f"📊 文件统计:")
    print(f"  - 总文件数: {validation_results['total_files']}")
    print(f"  - 存在文件: {validation_results['existing_files']}")
    print(f"  - 可读文件: {validation_results['readable_files']}")
    print(f"  - 总文件大小: {validation_results['total_size']:,} 字节")
    print(f"  - 文件成功率: {file_success_rate:.1f}%")
    
    print(f"\n📁 分类统计:")
    for category, stats in validation_results['categories'].items():
        success_rate = (stats['readable'] / stats['total'] * 100) if stats['total'] > 0 else 0
        print(f"  - {category}: {stats['readable']}/{stats['total']} ({success_rate:.1f}%)")
    
    print(f"\n🏗️ 源文件结构: {'✅ 完整' if structure_valid else '❌ 不完整'}")
    
    # 问题汇总
    if validation_results['missing_files'] or validation_results['issues']:
        print(f"\n⚠️ 发现的问题:")
        for missing in validation_results['missing_files']:
            print(f"  - 缺失文件: {missing}")
        for issue in validation_results['issues']:
            print(f"  - 问题: {issue}")
    else:
        print(f"\n🎉 没有发现问题!")
    
    # 总体评估
    overall_success = (
        file_success_rate >= 95 and 
        structure_valid and 
        len(validation_results['missing_files']) == 0 and
        len(validation_results['issues']) == 0
    )
    
    print(f"\n🎯 项目状态: {'✅ 完美完成' if overall_success else '⚠️ 需要注意'}")
    
    # 保存详细报告
    report_content = f"""
=== EST脚本转换测试用例项目最终验证报告 ===

验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 文件验证结果

总文件数: {validation_results['total_files']}
存在文件: {validation_results['existing_files']}
可读文件: {validation_results['readable_files']}
总文件大小: {validation_results['total_size']:,} 字节
文件成功率: {file_success_rate:.1f}%

## 分类统计
"""
    
    for category, stats in validation_results['categories'].items():
        success_rate = (stats['readable'] / stats['total'] * 100) if stats['total'] > 0 else 0
        report_content += f"\n{category}: {stats['readable']}/{stats['total']} ({success_rate:.1f}%)"
    
    report_content += f"""

## 源文件结构验证
EST源文件结构: {'完整' if structure_valid else '不完整'}

## 问题汇总
"""
    
    if validation_results['missing_files']:
        report_content += "\n缺失文件:\n"
        for missing in validation_results['missing_files']:
            report_content += f"- {missing}\n"
    
    if validation_results['issues']:
        report_content += "\n发现的问题:\n"
        for issue in validation_results['issues']:
            report_content += f"- {issue}\n"
    
    if not validation_results['missing_files'] and not validation_results['issues']:
        report_content += "\n✅ 没有发现问题!\n"
    
    report_content += f"""
## 总体评估
项目状态: {'✅ 完美完成' if overall_success else '⚠️ 需要注意'}

## 结论
{'🎉 EST脚本转换测试用例项目已完美完成，所有交付物都完整且可用！' if overall_success else '⚠️ 项目基本完成，但存在一些需要注意的问题。'}

验证完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""
    
    with open('最终验证报告.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"\n📄 详细报告已保存到: 最终验证报告.txt")
    
    return overall_success

def main():
    print("🚀 启动EST脚本转换测试用例项目最终验证...")
    
    # 验证项目交付物
    validation_results = validate_project_deliverables()
    
    # 验证EST源文件结构
    structure_valid = validate_est_source_structure()
    
    # 生成最终报告
    overall_success = generate_final_report(validation_results, structure_valid)
    
    if overall_success:
        print(f"\n🎉 恭喜！EST脚本转换测试用例项目已完美完成！")
        print(f"📋 所有交付物都完整且可用，项目可以正式交付使用。")
    else:
        print(f"\n⚠️ 项目基本完成，但请注意上述问题。")
    
    print(f"\n📊 项目验证完成于: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
