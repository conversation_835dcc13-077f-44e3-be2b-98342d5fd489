#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试用例Excel文件的简化版本
不依赖外部库，直接生成CSV格式，可以用Excel打开
"""

import os
import re
import csv
from pathlib import Path

def parse_est_file(file_path):
    """解析EST脚本文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        except:
            print(f"无法读取文件: {file_path}")
            return None
    
    # 提取基本信息
    test_case = {
        'file_name': os.path.basename(file_path),
        'function_name': '',
        'feature': '',
        'story': '',
        'steps': [],
        'category': ''
    }
    
    # 提取函数名
    func_match = re.search(r'def (test_\w+)', content)
    if func_match:
        test_case['function_name'] = func_match.group(1)
    
    # 提取feature和story
    feature_match = re.search(r'@allure\.feature\("([^"]+)"\)', content)
    if feature_match:
        test_case['feature'] = feature_match.group(1)
        
    story_match = re.search(r'@allure\.story\("([^"]+)"\)', content)
    if story_match:
        test_case['story'] = story_match.group(1)
    
    # 提取测试步骤
    steps = re.findall(r'with allure\.step\(\s*"""(.*?)"""\s*\):', content, re.DOTALL)
    for step in steps:
        clean_step = re.sub(r'\s+', ' ', step.strip())
        test_case['steps'].append(clean_step)
    
    return test_case

def scan_est_directory():
    """扫描EST目录"""
    test_cases = []
    est_path = Path("EST")
    
    if not est_path.exists():
        print("EST目录不存在")
        return test_cases
    
    # 扫描scenario目录
    scenario_dir = est_path / "scenario"
    if scenario_dir.exists():
        print(f"扫描scenario目录...")
        for py_file in scenario_dir.glob("*.py"):
            test_case = parse_est_file(str(py_file))
            if test_case:
                test_case['category'] = 'scenario'
                test_cases.append(test_case)
                print(f"  解析: {test_case['file_name']}")
    
    # 扫描stateflow目录
    stateflow_dir = est_path / "stateflow"
    if stateflow_dir.exists():
        print(f"扫描stateflow目录...")
        for subdir in stateflow_dir.iterdir():
            if subdir.is_dir():
                print(f"  扫描子目录: {subdir.name}")
                for py_file in subdir.glob("*.py"):
                    test_case = parse_est_file(str(py_file))
                    if test_case:
                        test_case['category'] = f'stateflow/{subdir.name}'
                        test_cases.append(test_case)
                        print(f"    解析: {test_case['file_name']}")
    
    return test_cases

def create_csv_file(test_cases, output_file):
    """创建CSV文件"""
    headers = [
        '序号', '测试用例编号', '测试用例名称', '功能模块', '测试类型', 
        '文件名', '前置条件', '测试步骤', '预期结果', '测试数据', '备注'
    ]
    
    with open(output_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(headers)
        
        for i, tc in enumerate(test_cases, 1):
            # 组装测试步骤
            steps_text = ""
            if tc['steps']:
                steps_text = "\n".join([f"{j+1}. {step}" for j, step in enumerate(tc['steps'])])
            
            # 前置条件
            precondition = ""
            if tc['steps'] and 'HIL台架初始化' in tc['steps'][0]:
                precondition = "HIL台架初始化且无故障情况"
            
            row = [
                i,  # 序号
                tc['function_name'],  # 测试用例编号
                tc['story'] or tc['function_name'],  # 测试用例名称
                tc['feature'],  # 功能模块
                tc['category'],  # 测试类型
                tc['file_name'],  # 文件名
                precondition,  # 前置条件
                steps_text,  # 测试步骤
                "验证EST状态转换正确",  # 预期结果
                "HIL仿真环境",  # 测试数据
                ""  # 备注
            ]
            writer.writerow(row)

def main():
    print("=== EST脚本转测试用例工具 ===")
    print("开始扫描EST脚本...")
    
    test_cases = scan_est_directory()
    
    if not test_cases:
        print("没有找到任何测试脚本")
        return
    
    print(f"\n总共找到 {len(test_cases)} 个测试脚本")
    
    # 按类别统计
    categories = {}
    for tc in test_cases:
        cat = tc['category']
        categories[cat] = categories.get(cat, 0) + 1
    
    print(f"\n按类别统计:")
    for cat, count in categories.items():
        print(f"  {cat}: {count} 个")
    
    # 生成CSV文件
    output_file = "EST测试用例.csv"
    create_csv_file(test_cases, output_file)
    print(f"\n测试用例已导出到: {output_file}")
    print("可以用Excel打开此CSV文件")
    
    # 显示前几个示例
    print(f"\n前5个测试用例示例:")
    for i, tc in enumerate(test_cases[:5]):
        print(f"  {i+1}. {tc['function_name']} - {tc['story']}")

if __name__ == "__main__":
    main()
