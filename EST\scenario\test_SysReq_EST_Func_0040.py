import time
from spack.simpkg import *
"""
0xAB02：ESTSts
        0x0:Off
        0x1:Passive
        0x2:Active Warning
        0x3:Active Stop
        0x4:Stand Still
        0x5:Active SpeedAdaption
        0x6:Active RightLaneChange
        0x7:Active Deceleration
        0x8:Active EmergenceLaneChange 
        0x9:Failure
"""

@allure.feature("est")
@allure.story("standstill2off")
def test_SysReq_EST_Func_0040(spkg: SimPackage):
    with allure.step(
        """
		HIL台架初始化且无故障情况
		场景选择直道三车道
		"""
    ):
        road_index = 8
        road_offset = 202.5
        # spkg.init_sim(level_name="T1Q04", road_index=road_index, wait_time_s=20)
        spkg.init_sim(road_index=road_index, road_offset=road_offset, distance=60000)

    with allure.step(
        """
        打开EST功能
        """
    ):
        spkg.canoe_someip.someip_signal_set(
            signal_name="DTE_ESASettingSt",
            signal_value=1,
        )
        time.sleep(0.1)

    with allure.step(
        """
		设置自车车速30kph
		"""
    ):
        spkg.set_ego_init_state(30, road_offset=road_offset)

    with allure.step(
        """
		配置项目工程路径
		"""
    ):
        test_case_name, blf_path, result_path, graphics_path = spkg.create_catalogue(
            parent_path=r"D:\TestProject\D4Q",
        )

    with allure.step(
        """
		配置需要绘图信号
		录制cve数据
		"""
    ):
        spkg.sim_api.set_plot_signals(
            graphics_path=graphics_path,
            test_case_name=test_case_name,
            signals=[
                "DTE_ESTSts",
                "DisplayVehicleSpeed",
                "ADS_EPS_1_SteerPilotAgEna",
                "ADS_1_PilotParkBrkDecTar",
            ],
        )
        spkg.sim_api.start_cve_record(test_case_name)
        time.sleep(0.1)

    with allure.step(
        """
        激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
		"""
    ):
        spkg.set_signal_value(
            bus_type="can",
            signal_name="GearShiftPos",
            target_value=5,
            reset_value=0,
            reset_time_ms=100,
        )
        time.sleep(0.1)
        spkg.meet_someip_value(
            signal_name="DTE_ACCSts",
            compare_method="==",
            target_value=3,
            time_ms=5000,
            only_once=True,
            thread=False,
        )
        time.sleep(0.1)
        spkg.set_signal_value(
            bus_type="can",
            signal_name="ICC_DriveDrosinessLevel",
            target_value=5,
        )

    with allure.step(
        """
        判断EST是否处于activewarning状态
        """
    ):
        spkg.meet_someip_value(
            signal_name="DTE_ESTSts",
            compare_method="==",
            target_value=2,
            time_ms=5000,
            only_once=True,
            thread=False,
        )
        time.sleep(30)

    with allure.step(
        """
        判断EST是否处于activestop状态
        """
    ):
        spkg.meet_someip_value(
            signal_name="DTE_ESTSts",
            compare_method="==",
            target_value=3,
            time_ms=15000,
            only_once=True,
            thread=False,
        )
        time.sleep(0.1)

    with allure.step(
        """
        判断EST是否处于standstill状态
        """
    ):
        spkg.meet_someip_value(
            signal_name="DTE_ESTSts",
            compare_method="==",
            target_value=4,
            time_ms=20000,
            only_once=True,
            thread=False,
        )
        time.sleep(0.1)

    with allure.step(
        """
		停止录制数据
		"""
    ):
        spkg.sim_api.stop_plot_data()
        spkg.stop_record(test_case_name)
        time.sleep(2)

    with allure.step(
        """
		台架状态复位
		"""
    ):
        spkg.reset_sim()
        time.sleep(1)