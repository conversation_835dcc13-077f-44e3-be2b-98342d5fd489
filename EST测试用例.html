<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>EST测试用例</title>
    <style>
        table {
            border-collapse: collapse;
            width: 100%;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .number {
            text-align: center;
            width: 50px;
        }
        .test-id {
            width: 200px;
            font-family: monospace;
        }
        .test-name {
            width: 150px;
        }
        .module {
            width: 80px;
            text-align: center;
        }
        .type {
            width: 120px;
        }
        .file {
            width: 180px;
            font-family: monospace;
            font-size: 10px;
        }
        .precondition {
            width: 150px;
        }
        .steps {
            width: 300px;
            white-space: pre-wrap;
        }
        .expected {
            width: 120px;
        }
        .data {
            width: 100px;
        }
        .remark {
            width: 80px;
        }
    </style>
</head>
<body>
    <h1>EST功能测试用例</h1>
    <p>生成时间: 2025-08-01 15:13:07</p>
    <p>总计: 133 个测试用例</p>
    
    <table>
        <tr>
            <th class="number">序号</th>
            <th class="test-id">测试用例编号</th>
            <th class="test-name">测试用例名称</th>
            <th class="module">功能模块</th>
            <th class="type">测试类型</th>
            <th class="file">文件名</th>
            <th class="precondition">前置条件</th>
            <th class="steps">测试步骤</th>
            <th class="expected">预期结果</th>
            <th class="data">测试数据</th>
            <th class="remark">备注</th>
        </tr>
        <tr>
            <td class="number">1</td>
            <td class="test-id">test_SysReq_EST_Func_0037</td>
            <td class="test-name">standstill2off</td>
            <td class="module">est</td>
            <td class="type">scenario</td>
            <td class="file">test_SysReq_EST_Func_0037.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速15kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activestop状态<br>9. 判断EST是否处于standstill状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">2</td>
            <td class="test-id">test_SysReq_EST_Func_0038</td>
            <td class="test-name">standstill2off</td>
            <td class="module">est</td>
            <td class="type">scenario</td>
            <td class="file">test_SysReq_EST_Func_0038.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速15kph 设置前方20m存在同速同向车辆<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>7. 判断EST是否处于activewarning状态 设置前车减速刹停<br>8. 判断EST是否处于activestop状态<br>9. 判断EST是否处于standstill状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">3</td>
            <td class="test-id">test_SysReq_EST_Func_0039</td>
            <td class="test-name">standstill2off</td>
            <td class="module">est</td>
            <td class="type">scenario</td>
            <td class="file">test_SysReq_EST_Func_0039.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速15kph 设置前方20m存在同速同向车辆<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>7. 判断EST是否处于activewarning状态 设置前车减速刹停<br>8. 判断EST是否处于activestop状态<br>9. 判断EST是否处于standstill状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">4</td>
            <td class="test-id">test_SysReq_EST_Func_0040</td>
            <td class="test-name">standstill2off</td>
            <td class="module">est</td>
            <td class="type">scenario</td>
            <td class="file">test_SysReq_EST_Func_0040.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速30kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activestop状态<br>9. 判断EST是否处于standstill状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">5</td>
            <td class="test-id">test_SysReq_EST_Func_0041</td>
            <td class="test-name">activerightlanechange2activestop</td>
            <td class="module">est</td>
            <td class="type">scenario</td>
            <td class="file">test_SysReq_EST_Func_0041.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速30kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activespeedadaption状态<br>9. 判断EST是否处于activerightlanechange状态<br>10. 判断EST是否处于activestop状态<br>11. 判断EST是否处于standstill状态<br>12. 停止录制数据<br>13. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">6</td>
            <td class="test-id">test_SysReq_EST_Func_0042</td>
            <td class="test-name">activewarning2activedeceleration</td>
            <td class="module">est</td>
            <td class="type">scenario</td>
            <td class="file">test_SysReq_EST_Func_0042.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速30kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activedeceleration状态<br>9. 判断EST是否处于activeemergencelanechange状态<br>10. 判断EST是否处于activestop状态<br>11. 判断EST是否处于standstill状态<br>12. 停止录制数据<br>13. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">7</td>
            <td class="test-id">test_SysReq_EST_Func_0043</td>
            <td class="test-name">activerightlanechange2activestop</td>
            <td class="module">est</td>
            <td class="type">scenario</td>
            <td class="file">test_SysReq_EST_Func_0043.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速30kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activespeedadaption状态<br>9. 判断EST是否处于activerightlanechange状态<br>10. 判断EST是否处于activedeceleration状态<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 判断EST是否处于activestop状态<br>13. 判断EST是否处于standstill状态<br>14. 停止录制数据<br>15. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">8</td>
            <td class="test-id">test_SysReq_EST_Func_0044</td>
            <td class="test-name">standstill2off</td>
            <td class="module">est</td>
            <td class="type">scenario</td>
            <td class="file">test_SysReq_EST_Func_0044.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速30kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activestop状态<br>9. 判断EST是否处于standstill状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">9</td>
            <td class="test-id">test_SysReq_EST_Func_0032_activedeceleration2activeemergencelanechange</td>
            <td class="test-name">activedeceleration2activeemergencelanechange</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actdec2actELC.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置速度显示大于20kph 判断EST是否处于activeemergencelanechange状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">10</td>
            <td class="test-id">test_SysReq_EST_Func_0034_activedeceleration2activestop</td>
            <td class="test-name">activedeceleration2activestop</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actdec2actstp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置速度显示小于20kph 判断EST是否处于activestop状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">11</td>
            <td class="test-id">test_SysReq_EST_Func_0033_activeemergencelanechange2activestop</td>
            <td class="test-name">activeemergencelanechange2activestop</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actELC2actstp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 判断EST是否处于activestop状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">12</td>
            <td class="test-id">test_SysReq_EST_Func_0029_activerightlanechange2activedeceleration</td>
            <td class="test-name">activerightlanechange2activedeceleration</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actRLC2actdec.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 判断EST是否处于activedeceleration状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">13</td>
            <td class="test-id">test_SysReq_EST_Func_0030_activerightlanechange2activestop</td>
            <td class="test-name">activerightlanechange2activestop</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actRLC2actstp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置速度显示小于20kph 判断EST是否处于activestop状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">14</td>
            <td class="test-id">test_SysReq_EST_Func_0028_activespeedadaption2activerightlanechange</td>
            <td class="test-name">activespeedadaption2activerightlanechange</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actspdadp2actRLC.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置速度显示大于20kph 判断EST是否处于activerightlanechange状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">15</td>
            <td class="test-id">test_SysReq_EST_Func_0030_activespeedadaption2activestop</td>
            <td class="test-name">activespeedadaption2activestop</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actspdadp2actstp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置速度显示小于20kph 判断EST是否处于activestop状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">16</td>
            <td class="test-id">test_SysReq_EST_Func_0027_activewarning2activedeceleration</td>
            <td class="test-name">activewarning2activedeceleration</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actwrn2actdec.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activedeceleration状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">17</td>
            <td class="test-id">test_SysReq_EST_Func_0026_activewarning2activespeedadaption</td>
            <td class="test-name">activewarning2activespeedadaption</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actwrn2actspdadp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activespeedadaption状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">18</td>
            <td class="test-id">test_SysReq_EST_Func_0025_activewarning2activestop</td>
            <td class="test-name">activewarning2activestop</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2active</td>
            <td class="file">test_actwrn2actstp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 判断EST是否处于activestop状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">19</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_4L</td>
            <td class="test-name">activedeceleration2passive_4L</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_4L.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">20</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_ABS</td>
            <td class="test-name">activedeceleration2passive_ABS</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_ABS.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 激活ABS 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">21</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_acl</td>
            <td class="test-name">activedeceleration2passive_acl</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_acl.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置油门开度大于70% 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">22</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_AEB</td>
            <td class="test-name">activedeceleration2passive_AEB</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_AEB.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 激活AEB 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">23</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_brk</td>
            <td class="test-name">activedeceleration2passive_brk</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_brk.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置刹车压力大于10bar 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">24</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_CCO</td>
            <td class="test-name">activedeceleration2passive_CCO</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_CCO.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">25</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_clb</td>
            <td class="test-name">activedeceleration2passive_clb</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_clb.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">26</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_curv</td>
            <td class="test-name">activedeceleration2passive_curv</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_curv.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">27</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_drvhan</td>
            <td class="test-name">activedeceleration2passive_drvhan</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_drvhan.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">28</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_drvmod3</td>
            <td class="test-name">activedeceleration2passive_drvmod3</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_drvmod3.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">29</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_ESP</td>
            <td class="test-name">activedeceleration2passive_ESP</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_ESP.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 激活ESP 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">30</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_ftgoff</td>
            <td class="test-name">activedeceleration2passive_ftgoff</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_ftgoff.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 关闭疲劳检测 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">31</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_gsft</td>
            <td class="test-name">activedeceleration2passive_gsft</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_gsft.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">32</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_lanlos</td>
            <td class="test-name">activedeceleration2passive_lanlos</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_lanlos.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置车道线探测丢失 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">33</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_spd140</td>
            <td class="test-name">activedeceleration2passive_spd140</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_spd140.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置速度大于135kph 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">34</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2passive_trnsp</td>
            <td class="test-name">activedeceleration2passive_trnsp</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actdec2psv_trnsp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">35</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_4L</td>
            <td class="test-name">activeemergencelanechange2passive_4L</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_4L.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置特殊驾驶模式 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">36</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ABS</td>
            <td class="test-name">activeemergencelanechange2passive_ABS</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_ABS.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 激活ABS 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">37</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_acl</td>
            <td class="test-name">activeemergencelanechange2passive_acl</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_acl.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置油门开度大于70% 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">38</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_AEB</td>
            <td class="test-name">activeemergencelanechange2passive_AEB</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_AEB.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 激活AEB 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">39</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_brk</td>
            <td class="test-name">activeemergencelanechange2passive_brk</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_brk.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置刹车压力大于10bar 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">40</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_CCO</td>
            <td class="test-name">activeemergencelanechange2passive_CCO</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_CCO.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置特殊驾驶模式 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">41</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_clb</td>
            <td class="test-name">activeemergencelanechange2passive_clb</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_clb.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置特殊驾驶模式 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">42</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_drvhan</td>
            <td class="test-name">activeemergencelanechange2passive_drvhan</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_drvhan.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">43</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_drvmod3</td>
            <td class="test-name">activeemergencelanechange2passive_drvmod3</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_drvmod3.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置特殊驾驶模式 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">44</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ESP</td>
            <td class="test-name">activeemergencelanechange2passive_ESP</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_ESP.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 激活ESP 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">45</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ftgoff</td>
            <td class="test-name">activeemergencelanechange2passive_ftgoff</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_ftgoff.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 关闭疲劳检测 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">46</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_gsft</td>
            <td class="test-name">activeemergencelanechange2passive_gsft</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_gsft.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置上拨拨杆退出ACC 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">47</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_lanlos</td>
            <td class="test-name">activeemergencelanechange2passive_lanlos</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_lanlos.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置车道线探测丢失 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">48</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_spd140</td>
            <td class="test-name">activeemergencelanechange2passive_spd140</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_spd140.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置速度大于135kph 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">49</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2passive_trnsp</td>
            <td class="test-name">activeemergencelanechange2passive_trnsp</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actELC2psv_trnsp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 设置特殊驾驶模式 判断EST是否处于passive状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">50</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_4L</td>
            <td class="test-name">activerightlanechange2passive_4L</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_4L.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置特殊驾驶模式 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">51</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_ABS</td>
            <td class="test-name">activerightlanechange2passive_ABS</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_ABS.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 激活ABS 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">52</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_acl</td>
            <td class="test-name">activerightlanechange2passive_acl</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_acl.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置油门开度大于70% 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">53</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_AEB</td>
            <td class="test-name">activerightlanechange2passive_AEB</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_AEB.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 激活AEB 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">54</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_brk</td>
            <td class="test-name">activerightlanechange2passive_brk</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_brk.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置刹车压力大于10bar 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">55</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_CCO</td>
            <td class="test-name">activerightlanechange2passive_CCO</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_CCO.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置特殊驾驶模式 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">56</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_clb</td>
            <td class="test-name">activerightlanechange2passive_clb</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_clb.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置特殊驾驶模式 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">57</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_curv</td>
            <td class="test-name">activerightlanechange2passive_curv</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_curv.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 进入曲率大于0.004的弯道 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">58</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_drvhan</td>
            <td class="test-name">activerightlanechange2passive_drvhan</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_drvhan.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">59</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_drvmod3</td>
            <td class="test-name">activerightlanechange2passive_drvmod3</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_drvmod3.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置特殊驾驶模式 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">60</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_ESP</td>
            <td class="test-name">activerightlanechange2passive_ESP</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_ESP.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 激活ESP 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">61</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_ftgoff</td>
            <td class="test-name">activerightlanechange2passive_ftgoff</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_ftgoff.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 关闭疲劳检测 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">62</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_gsft</td>
            <td class="test-name">activerightlanechange2passive_gsft</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_gsft.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置上拨拨杆退出ACC 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">63</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_lanlos</td>
            <td class="test-name">activerightlanechange2passive_lanlos</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_lanlos.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置车道线探测丢失 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">64</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_spd140</td>
            <td class="test-name">activerightlanechange2passive_spd140</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_spd140.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置速度大于135kph 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">65</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2passive_trnsp</td>
            <td class="test-name">activerightlanechange2passive_trnsp</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actRLC2psv_trnsp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 设置特殊驾驶模式 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">66</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_4L</td>
            <td class="test-name">activespeedadaption2passive_4L</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_4L.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">67</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_ABS</td>
            <td class="test-name">activespeedadaption2passive_ABS</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_ABS.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 激活ABS 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">68</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_acl</td>
            <td class="test-name">activespeedadaption2passive_acl</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_acl.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置油门开度大于70% 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">69</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_AEB</td>
            <td class="test-name">activespeedadaption2passive_AEB</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_AEB.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 激活AEB 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">70</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_brk</td>
            <td class="test-name">activespeedadaption2passive_brk</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_brk.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置刹车压力大于10bar 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">71</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_CCO</td>
            <td class="test-name">activespeedadaption2passive_CCO</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_CCO.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">72</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_clb</td>
            <td class="test-name">activespeedadaption2passive_clb</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_clb.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">73</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_curv</td>
            <td class="test-name">activespeedadaption2passive_curv</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_curv.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">74</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_drvhan</td>
            <td class="test-name">activespeedadaption2passive_drvhan</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_drvhan.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">75</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_drvmod3</td>
            <td class="test-name">activespeedadaption2passive_drvmod3</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_drvmod3.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">76</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_ESP</td>
            <td class="test-name">activespeedadaption2passive_ESP</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_ESP.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 激活ESP 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">77</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_ftgoff</td>
            <td class="test-name">activespeedadaption2passive_ftgoff</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_ftgoff.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 关闭疲劳检测 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">78</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_gsft</td>
            <td class="test-name">activespeedadaption2passive_gsft</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_gsft.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">79</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_lanlos</td>
            <td class="test-name">activespeedadaption2passive_lanlos</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_lanlos.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置车道线探测丢失 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">80</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_spd140</td>
            <td class="test-name">activespeedadaption2passive_spd140</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_spd140.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置速度大于135kph 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">81</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2passive_trnsp</td>
            <td class="test-name">activespeedadaption2passive_trnsp</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actspdadp2psv_trnsp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">82</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_4L</td>
            <td class="test-name">activestop2passive_4L</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_4L.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">83</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_ABS</td>
            <td class="test-name">activestop2passive_ABS</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_ABS.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 激活ABS 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">84</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_acl</td>
            <td class="test-name">activestop2passive_acl</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_acl.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置油门开度大于70% 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">85</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_AEB</td>
            <td class="test-name">activestop2passive_AEB</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_AEB.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 激活AEB 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">86</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_brk</td>
            <td class="test-name">activestop2passive_brk</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_brk.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置刹车压力大于10bar 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">87</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_CCO</td>
            <td class="test-name">activestop2passive_CCO</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_CCO.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">88</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_clb</td>
            <td class="test-name">activestop2passive_clb</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_clb.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">89</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_curv</td>
            <td class="test-name">activestop2passive_curv</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_curv.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速15kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">90</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_drvhan</td>
            <td class="test-name">activestop2passive_drvhan</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_drvhan.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">91</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_drvmod3</td>
            <td class="test-name">activestop2passive_drvmod3</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_drvmod3.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">92</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_ESP</td>
            <td class="test-name">activestop2passive_ESP</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_ESP.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 激活ESP 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">93</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_ftgoff</td>
            <td class="test-name">activestop2passive_ftgoff</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_ftgoff.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 关闭疲劳检测 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">94</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_gsft</td>
            <td class="test-name">activestop2passive_gsft</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_gsft.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">95</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_lanlos</td>
            <td class="test-name">activestop2passive_lanlos</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_lanlos.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置车道线探测丢失 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">96</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_spd140</td>
            <td class="test-name">activestop2passive_spd140</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_spd140.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置速度大于135kph 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">97</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2passive_trnsp</td>
            <td class="test-name">activestop2passive_trnsp</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actstp2psv_trnsp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置特殊驾驶模式 判断EST是否处于passive状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">98</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_4L</td>
            <td class="test-name">activewarning2passive_4L</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_4L.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置特殊驾驶模式 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">99</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_ABS</td>
            <td class="test-name">activewarning2passive_ABS</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_ABS.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 激活ABS 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">100</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_acl</td>
            <td class="test-name">activewarning2passive_acl</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_acl.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置油门开度大于70% 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">101</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_AEB</td>
            <td class="test-name">activewarning2passive_AEB</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_AEB.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 激活AEB 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">102</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_brk</td>
            <td class="test-name">activewarning2passive_brk</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_brk.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置刹车压力大于10bar 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">103</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_CCO</td>
            <td class="test-name">activewarning2passive_CCO</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_CCO.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置特殊驾驶模式 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">104</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_clb</td>
            <td class="test-name">activewarning2passive_clb</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_clb.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置特殊驾驶模式 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">105</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_curv</td>
            <td class="test-name">activewarning2passive_curv</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_curv.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 进入曲率大于0.004的弯道 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">106</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_drvhan</td>
            <td class="test-name">activewarning2passive_drvhan</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_drvhan.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">107</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_drvmod3</td>
            <td class="test-name">activewarning2passive_drvmod3</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_drvmod3.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置特殊驾驶模式 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">108</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_ESP</td>
            <td class="test-name">activewarning2passive_ESP</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_ESP.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 激活ESP 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">109</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_ftgoff</td>
            <td class="test-name">activewarning2passive_ftgoff</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_ftgoff.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 关闭疲劳检测 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">110</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_gsft</td>
            <td class="test-name">activewarning2passive_gsft</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_gsft.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置上拨拨杆退出ACC 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">111</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_lanlos</td>
            <td class="test-name">activewarning2passive_lanlos</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_lanlos.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置车道线探测丢失 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">112</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_spd140</td>
            <td class="test-name">activewarning2passive_spd140</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_spd140.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置速度大于135kph 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">113</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2passive_trnsp</td>
            <td class="test-name">activewarning2passive_trnsp</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2passive</td>
            <td class="file">test_actwrn2psv_trnsp.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 设置特殊驾驶模式 判断EST是否处于passive状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">114</td>
            <td class="test-id">test_SysReq_EST_Func_0035_activestop2standstill</td>
            <td class="test-name">activestop2standstill</td>
            <td class="module">est</td>
            <td class="type">stateflow/active2standstill</td>
            <td class="file">test_actstp2stdstill.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 设置车辆静止 判断EST是否处于standstill状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">115</td>
            <td class="test-id">test_SysReq_EST_Func_0023_passive2fail</td>
            <td class="test-name">passive2fail</td>
            <td class="module">est</td>
            <td class="type">stateflow/fail2passive</td>
            <td class="file">test_fail2psv.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态<br>7. 取消注入故障 判断EST是否处于passive状态<br>8. 停止录制数据<br>9. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">116</td>
            <td class="test-id">test_SysReq_SysReq_EST_Func_0019_off2passive</td>
            <td class="test-name">off2passive</td>
            <td class="module">est</td>
            <td class="type">stateflow/off2passive</td>
            <td class="file">test_off2passive.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 设置自车车速25kph<br>3. 配置项目工程路径<br>4. 配置需要绘图信号 录制cve数据<br>5. 关闭EST功能 判断EST是否处于off状态<br>6. 打开EST功能 判断EST是否处于passive状态<br>7. 停止录制数据<br>8. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">117</td>
            <td class="test-id">test_SysReq_EST_Func_0020_activedeceleration2off</td>
            <td class="test-name">activedeceleration2off</td>
            <td class="module">est</td>
            <td class="type">stateflow/on2off</td>
            <td class="file">test_actdec2off.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 关闭EST功能 判断EST是否处于off状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">118</td>
            <td class="test-id">test_SysReq_EST_Func_0020_activeemergencelanechange2off</td>
            <td class="test-name">activeemergencelanechange2off</td>
            <td class="module">est</td>
            <td class="type">stateflow/on2off</td>
            <td class="file">test_actELC2off.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 关闭EST功能 判断EST是否处于off状态<br>13. 停止录制数据<br>14. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">119</td>
            <td class="test-id">test_SysReq_EST_Func_0020_activerightlanechange2off</td>
            <td class="test-name">activerightlanechange2off</td>
            <td class="module">est</td>
            <td class="type">stateflow/on2off</td>
            <td class="file">test_actRLC2off.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 关闭EST功能 判断EST是否处于off状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">120</td>
            <td class="test-id">test_SysReq_EST_Func_0020_activespeedadaption2off</td>
            <td class="test-name">activespeedadaption2off</td>
            <td class="module">est</td>
            <td class="type">stateflow/on2off</td>
            <td class="file">test_actspdadp2off.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 关闭EST功能 判断EST是否处于off状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">121</td>
            <td class="test-id">test_SysReq_EST_Func_0020_activestop2off</td>
            <td class="test-name">activestop2off</td>
            <td class="module">est</td>
            <td class="type">stateflow/on2off</td>
            <td class="file">test_actstp2off.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 关闭EST功能 判断EST是否处于off状态<br>10. 停止录制数据<br>11. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">122</td>
            <td class="test-id">test_SysReq_EST_Func_0020_activewarning2off</td>
            <td class="test-name">activewarning2off</td>
            <td class="module">est</td>
            <td class="type">stateflow/on2off</td>
            <td class="file">test_actwrn2off.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 关闭EST功能 判断EST是否处于off状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">123</td>
            <td class="test-id">test_SysReq_EST_Func_0020_passive2off</td>
            <td class="test-name">passive2off</td>
            <td class="module">est</td>
            <td class="type">stateflow/on2off</td>
            <td class="file">test_passive2off.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 设置自车车速25kph<br>3. 配置项目工程路径<br>4. 配置需要绘图信号 录制cve数据<br>5. 打开EST功能 判断EST是否处于passive状态<br>6. 关闭EST功能 判断EST是否处于off状态<br>7. 停止录制数据<br>8. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">124</td>
            <td class="test-id">test_SysReq_EST_Func_0035_standstill2off</td>
            <td class="test-name">standstill2off</td>
            <td class="module">est</td>
            <td class="type">stateflow/on2off</td>
            <td class="file">test_stdstl2off.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activestop状态 设置车辆静止<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于standstill状态<br>10. 关闭EST功能 判断EST是否处于off状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">125</td>
            <td class="test-id">test_SysReq_EST_Func_0024_passive2activewarning</td>
            <td class="test-name">passive2activewarning</td>
            <td class="module">est</td>
            <td class="type">stateflow/passive2actve_warning</td>
            <td class="file">test_psv2actwrn.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 判断EST是否处于passive状态<br>7. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>8. 判断EST是否处于activewarning状态<br>9. 停止录制数据<br>10. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">126</td>
            <td class="test-id">test_SysReq_EST_Func_0035_standstill2passive</td>
            <td class="test-name">standstill2passive</td>
            <td class="module">est</td>
            <td class="type">stateflow/standstill2passive</td>
            <td class="file">test_stdstl2passive.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activestop状态 设置车辆静止<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于standstill状态<br>10. 设置车辆非静止 判断EST是否处于passive状态<br>11. 停止录制数据<br>12. 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">127</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activedeceleration2fail</td>
            <td class="test-name">activedeceleration2fail</td>
            <td class="module">est</td>
            <td class="type">stateflow/state2failure</td>
            <td class="file">test_actdec2fail.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activedeceleration状态<br>9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态<br>10. 停止录制数据<br>11. 取消注入故障 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">128</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activeemergencelanechange2fail</td>
            <td class="test-name">activeemergencelanechange2fail</td>
            <td class="module">est</td>
            <td class="type">stateflow/state2failure</td>
            <td class="file">test_actELC2fail.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 判断EST是否处于activerightlanechange状态<br>8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph<br>9. 配置项目工程路径<br>10. 配置需要绘图信号 录制cve数据<br>11. 判断EST是否处于activeemergencelanechange状态<br>12. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态<br>13. 停止录制数据<br>14. 取消注入故障 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">129</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activerightlanechange2fail</td>
            <td class="test-name">activerightlanechange2fail</td>
            <td class="module">est</td>
            <td class="type">stateflow/state2failure</td>
            <td class="file">test_actRLC2fail.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph<br>7. 配置项目工程路径<br>8. 配置需要绘图信号 录制cve数据<br>9. 判断EST是否处于activerightlanechange状态<br>10. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态<br>11. 停止录制数据<br>12. 取消注入故障 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">130</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activespeedadaption2fail</td>
            <td class="test-name">activespeedadaption2fail</td>
            <td class="module">est</td>
            <td class="type">stateflow/state2failure</td>
            <td class="file">test_actspdadp2fail.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activespeedadaption状态<br>9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态<br>10. 停止录制数据<br>11. 取消注入故障 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">131</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activestop2fail</td>
            <td class="test-name">activestop2fail</td>
            <td class="module">est</td>
            <td class="type">stateflow/state2failure</td>
            <td class="file">test_actstp2fail.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 判断EST是否处于activewarning状态<br>6. 配置项目工程路径<br>7. 配置需要绘图信号 录制cve数据<br>8. 判断EST是否处于activestop状态<br>9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态<br>10. 停止录制数据<br>11. 取消注入故障 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">132</td>
            <td class="test-id">test_SysReq_EST_Func_0023_activewarning2fail</td>
            <td class="test-name">activewarning2fail</td>
            <td class="module">est</td>
            <td class="type">stateflow/state2failure</td>
            <td class="file">test_actwrn2fail.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳<br>5. 配置项目工程路径<br>6. 配置需要绘图信号 录制cve数据<br>7. 判断EST是否处于activewarning状态<br>8. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态<br>9. 停止录制数据<br>10. 取消注入故障 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
        <tr>
            <td class="number">133</td>
            <td class="test-id">test_SysReq_EST_Func_0023_passive2fail</td>
            <td class="test-name">passive2fail</td>
            <td class="module">est</td>
            <td class="type">stateflow/state2failure</td>
            <td class="file">test_psv2fail.py</td>
            <td class="precondition">HIL台架初始化且无故障情况</td>
            <td class="steps">1. HIL台架初始化且无故障情况 场景选择直道三车道<br>2. 打开EST功能<br>3. 设置自车车速25kph<br>4. 配置项目工程路径<br>5. 配置需要绘图信号 录制cve数据<br>6. 判断EST是否处于passive状态<br>7. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态<br>8. 停止录制数据<br>9. 取消注入故障 台架状态复位</td>
            <td class="expected">验证EST状态转换正确</td>
            <td class="data">HIL仿真环境</td>
            <td class="remark"></td>
        </tr>
    </table>
</body>
</html>