#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将CSV文件转换为Excel格式的脚本
"""

import csv
import os
from datetime import datetime

def csv_to_excel_html(csv_file, output_file):
    """将CSV转换为HTML表格格式，可以用Excel打开"""
    
    # 读取CSV文件
    rows = []
    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            for row in reader:
                rows.append(row)
    except:
        try:
            with open(csv_file, 'r', encoding='gbk') as f:
                reader = csv.reader(f)
                for row in reader:
                    rows.append(row)
        except Exception as e:
            print(f"读取CSV文件失败: {e}")
            return
    
    if not rows:
        print("CSV文件为空")
        return
    
    # 生成HTML表格
    html_content = f"""<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>EST测试用例</title>
    <style>
        table {{
            border-collapse: collapse;
            width: 100%;
            font-family: Arial, sans-serif;
            font-size: 12px;
        }}
        th, td {{
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }}
        th {{
            background-color: #f2f2f2;
            font-weight: bold;
        }}
        .number {{
            text-align: center;
            width: 50px;
        }}
        .test-id {{
            width: 200px;
            font-family: monospace;
        }}
        .test-name {{
            width: 150px;
        }}
        .module {{
            width: 80px;
            text-align: center;
        }}
        .type {{
            width: 120px;
        }}
        .file {{
            width: 180px;
            font-family: monospace;
            font-size: 10px;
        }}
        .precondition {{
            width: 150px;
        }}
        .steps {{
            width: 300px;
            white-space: pre-wrap;
        }}
        .expected {{
            width: 120px;
        }}
        .data {{
            width: 100px;
        }}
        .remark {{
            width: 80px;
        }}
    </style>
</head>
<body>
    <h1>EST功能测试用例</h1>
    <p>生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
    <p>总计: {len(rows)-1} 个测试用例</p>
    
    <table>
"""
    
    # 添加表头
    if rows:
        html_content += "        <tr>\n"
        headers = rows[0]
        header_classes = ['number', 'test-id', 'test-name', 'module', 'type', 'file', 'precondition', 'steps', 'expected', 'data', 'remark']
        for i, header in enumerate(headers):
            class_name = header_classes[i] if i < len(header_classes) else ''
            html_content += f'            <th class="{class_name}">{header}</th>\n'
        html_content += "        </tr>\n"
    
    # 添加数据行
    for row_idx, row in enumerate(rows[1:], 1):
        if not any(row):  # 跳过空行
            continue
            
        html_content += "        <tr>\n"
        for i, cell in enumerate(row):
            class_name = header_classes[i] if i < len(header_classes) else ''
            # 处理换行符
            cell_content = str(cell).replace('\n', '<br>')
            html_content += f'            <td class="{class_name}">{cell_content}</td>\n'
        html_content += "        </tr>\n"
    
    html_content += """    </table>
</body>
</html>"""
    
    # 保存HTML文件
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        print(f"HTML文件已生成: {output_file}")
        print("可以用Excel或浏览器打开此文件")
    except Exception as e:
        print(f"保存HTML文件失败: {e}")

def create_summary_report():
    """创建测试用例摘要报告"""
    
    # 读取CSV文件
    rows = []
    try:
        with open('EST测试用例.csv', 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            for row in reader:
                rows.append(row)
    except:
        print("无法读取CSV文件")
        return
    
    if len(rows) < 2:
        print("CSV文件数据不足")
        return
    
    # 统计信息
    total_cases = len(rows) - 1  # 减去表头
    
    # 按测试类型统计
    type_stats = {}
    module_stats = {}
    
    for row in rows[1:]:
        if len(row) >= 5:
            test_type = row[4]  # 测试类型
            module = row[3]     # 功能模块
            
            type_stats[test_type] = type_stats.get(test_type, 0) + 1
            module_stats[module] = module_stats.get(module, 0) + 1
    
    # 生成摘要报告
    report = f"""
=== EST测试用例转换摘要报告 ===

生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
总测试用例数: {total_cases}

按测试类型统计:
"""
    
    for test_type, count in sorted(type_stats.items()):
        report += f"  {test_type}: {count} 个\n"
    
    report += f"\n按功能模块统计:\n"
    for module, count in sorted(module_stats.items()):
        report += f"  {module}: {count} 个\n"
    
    report += f"""
文件说明:
- EST测试用例.csv: 原始CSV格式测试用例
- EST测试用例.html: HTML格式测试用例（可用Excel打开）
- D4Q_L2功能测试用例_20250725.xlsx: 原始模板文件

使用说明:
1. 可以直接用Excel打开HTML文件进行编辑
2. CSV文件可以导入到任何支持CSV的工具中
3. 测试步骤已经按照EST脚本的allure.step进行了结构化整理
4. 每个测试用例都包含了完整的前置条件、测试步骤和预期结果

转换完成！
"""
    
    print(report)
    
    # 保存报告到文件
    with open('转换摘要报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

def main():
    print("=== EST测试用例格式转换工具 ===")
    
    csv_file = "EST测试用例.csv"
    html_file = "EST测试用例.html"
    
    if not os.path.exists(csv_file):
        print(f"CSV文件不存在: {csv_file}")
        return
    
    # 转换为HTML格式
    print("正在转换CSV到HTML格式...")
    csv_to_excel_html(csv_file, html_file)
    
    # 生成摘要报告
    print("\n正在生成摘要报告...")
    create_summary_report()
    
    print(f"\n转换完成！生成的文件:")
    print(f"- {html_file} (可用Excel打开)")
    print(f"- 转换摘要报告.txt")

if __name__ == "__main__":
    main()
