#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的Excel文件质量和完整性
"""

import os
import openpyxl
import csv
from datetime import datetime

def validate_excel_file(excel_file):
    """验证Excel文件的完整性和质量"""
    
    print(f"=== 验证Excel文件: {excel_file} ===")
    
    if not os.path.exists(excel_file):
        print(f"❌ 文件不存在: {excel_file}")
        return False
    
    validation_results = {
        'file_exists': True,
        'file_readable': False,
        'est_sheet_exists': False,
        'headers_correct': False,
        'data_count': 0,
        'expected_count': 133,
        'data_complete': False,
        'format_correct': False,
        'issues': []
    }
    
    try:
        # 尝试打开Excel文件
        workbook = openpyxl.load_workbook(excel_file, data_only=True)
        validation_results['file_readable'] = True
        print("✅ 文件可以正常读取")
        
        # 检查EST工作表是否存在
        if 'EST' in workbook.sheetnames:
            validation_results['est_sheet_exists'] = True
            print("✅ EST工作表存在")
            
            worksheet = workbook['EST']
            
            # 检查表头
            expected_headers = [
                '测试用例ID', '系统需求ID', '需求描述', '前置条件', 
                '测试环境', '测试步骤和期望结果', '文件路径', '测试结果', '备注'
            ]
            
            actual_headers = []
            for col in range(1, 10):  # 9列
                cell = worksheet.cell(row=1, column=col)
                if cell.value:
                    actual_headers.append(str(cell.value).strip())
                else:
                    actual_headers.append("")
            
            if actual_headers == expected_headers:
                validation_results['headers_correct'] = True
                print("✅ 表头格式正确")
            else:
                validation_results['issues'].append(f"表头不匹配: 期望 {expected_headers}, 实际 {actual_headers}")
                print(f"❌ 表头格式错误")
            
            # 统计数据行数
            data_rows = 0
            for row in range(2, worksheet.max_row + 1):
                # 检查第一列是否有数据
                cell = worksheet.cell(row=row, column=1)
                if cell.value and str(cell.value).strip():
                    data_rows += 1
            
            validation_results['data_count'] = data_rows
            print(f"📊 数据行数: {data_rows}")
            
            # 检查数据完整性
            if data_rows == validation_results['expected_count']:
                validation_results['data_complete'] = True
                print("✅ 数据完整性检查通过")
            else:
                validation_results['issues'].append(f"数据行数不匹配: 期望 {validation_results['expected_count']}, 实际 {data_rows}")
                print(f"❌ 数据完整性检查失败")
            
            # 检查关键字段是否有数据
            empty_fields = 0
            total_fields = 0
            
            for row in range(2, min(worksheet.max_row + 1, 10)):  # 检查前8行作为样本
                for col in range(1, 7):  # 检查前6列的关键字段
                    cell = worksheet.cell(row=row, column=col)
                    total_fields += 1
                    if not cell.value or not str(cell.value).strip():
                        empty_fields += 1
            
            if total_fields > 0:
                empty_rate = empty_fields / total_fields
                if empty_rate < 0.1:  # 空字段率小于10%
                    validation_results['format_correct'] = True
                    print(f"✅ 数据格式检查通过 (空字段率: {empty_rate:.1%})")
                else:
                    validation_results['issues'].append(f"空字段率过高: {empty_rate:.1%}")
                    print(f"⚠️ 数据格式需要注意 (空字段率: {empty_rate:.1%})")
            
        else:
            validation_results['issues'].append("EST工作表不存在")
            print("❌ EST工作表不存在")
        
        workbook.close()
        
    except Exception as e:
        validation_results['issues'].append(f"读取文件时出错: {str(e)}")
        print(f"❌ 读取文件时出错: {e}")
    
    return validation_results

def compare_with_source_data():
    """与源数据进行对比验证"""
    
    print(f"\n=== 与源数据对比验证 ===")
    
    csv_file = "EST测试用例.csv"
    if not os.path.exists(csv_file):
        print("❌ 源CSV文件不存在，无法进行对比")
        return False
    
    try:
        # 读取CSV数据
        csv_data = []
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                csv_data.append(row)
        
        print(f"✅ CSV源数据: {len(csv_data)} 条记录")
        
        # 简单统计对比
        scenario_count = len([row for row in csv_data if 'scenario' in row.get('测试类型', '')])
        stateflow_count = len([row for row in csv_data if 'stateflow' in row.get('测试类型', '')])
        
        print(f"📊 scenario类型: {scenario_count} 个")
        print(f"📊 stateflow类型: {stateflow_count} 个")
        print(f"📊 总计: {len(csv_data)} 个")
        
        return True
        
    except Exception as e:
        print(f"❌ 对比验证失败: {e}")
        return False

def generate_validation_report(validation_results):
    """生成验证报告"""
    
    report_content = f"""
=== EST测试用例Excel文件验证报告 ===

验证时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 验证结果摘要

✅ 通过项目:
"""
    
    if validation_results['file_exists']:
        report_content += "- 文件存在性检查\n"
    if validation_results['file_readable']:
        report_content += "- 文件可读性检查\n"
    if validation_results['est_sheet_exists']:
        report_content += "- EST工作表存在性检查\n"
    if validation_results['headers_correct']:
        report_content += "- 表头格式检查\n"
    if validation_results['data_complete']:
        report_content += "- 数据完整性检查\n"
    if validation_results['format_correct']:
        report_content += "- 数据格式检查\n"
    
    report_content += f"""
## 数据统计

- 期望测试用例数量: {validation_results['expected_count']}
- 实际测试用例数量: {validation_results['data_count']}
- 数据完整率: {(validation_results['data_count'] / validation_results['expected_count'] * 100):.1f}%

## 质量评估

"""
    
    if not validation_results['issues']:
        report_content += "🎉 所有检查项目均通过，文件质量优秀！\n"
    else:
        report_content += "⚠️ 发现以下问题:\n"
        for issue in validation_results['issues']:
            report_content += f"- {issue}\n"
    
    report_content += f"""
## 使用建议

1. 生成的Excel文件可以直接用于测试管理
2. 建议在使用前进行人工抽查验证
3. 可以根据实际需要调整测试步骤的详细程度
4. 文件路径字段可以根据实际项目结构进行调整

## 文件信息

- 原始模板: D4Q_L2功能测试用例_20250725.xlsx
- 生成文件: D4Q_L2功能测试用例_EST更新_20250801_153058.xlsx
- 源数据: EST测试用例.csv

验证完成！
"""
    
    # 保存报告
    with open('Excel文件验证报告.txt', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(report_content)

def main():
    print("=== EST测试用例Excel文件质量验证工具 ===")
    
    # 查找最新的Excel文件
    excel_files = [f for f in os.listdir('.') if f.startswith('D4Q_L2功能测试用例_EST更新_') and f.endswith('.xlsx')]
    
    if not excel_files:
        print("❌ 没有找到生成的Excel文件")
        return
    
    # 选择最新的文件
    latest_file = sorted(excel_files)[-1]
    print(f"🔍 验证文件: {latest_file}")
    
    # 执行验证
    validation_results = validate_excel_file(latest_file)
    
    # 与源数据对比
    compare_with_source_data()
    
    # 生成报告
    generate_validation_report(validation_results)

if __name__ == "__main__":
    main()
