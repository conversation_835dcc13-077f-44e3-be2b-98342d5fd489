#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析原始Excel模板并将EST测试用例写入到模板中
"""

import pandas as pd
import openpyxl
from openpyxl.styles import Font, Alignment, Border, Side, PatternFill
import csv
import os
from datetime import datetime

def analyze_original_template():
    """分析原始Excel模板的结构"""
    template_file = "D4Q_L2功能测试用例_20250725.xlsx"
    
    if not os.path.exists(template_file):
        print(f"模板文件不存在: {template_file}")
        return None
    
    try:
        # 使用openpyxl读取Excel文件
        workbook = openpyxl.load_workbook(template_file, data_only=False)
        
        print(f"=== 分析Excel模板文件 ===")
        print(f"工作表列表: {workbook.sheetnames}")
        
        template_info = {}
        
        for sheet_name in workbook.sheetnames:
            print(f"\n--- 工作表: {sheet_name} ---")
            worksheet = workbook[sheet_name]
            
            # 获取工作表的尺寸
            max_row = worksheet.max_row
            max_col = worksheet.max_column
            print(f"最大行数: {max_row}, 最大列数: {max_col}")
            
            # 分析表头结构
            headers = []
            for col in range(1, max_col + 1):
                cell = worksheet.cell(row=1, column=col)
                if cell.value:
                    headers.append(str(cell.value).strip())
                else:
                    headers.append("")
            
            print(f"表头: {headers}")
            
            # 查找关键列
            key_columns = {}
            for i, header in enumerate(headers):
                if header:
                    key_columns[header] = i + 1
            
            template_info[sheet_name] = {
                'max_row': max_row,
                'max_col': max_col,
                'headers': headers,
                'key_columns': key_columns
            }
            
            # 显示前几行数据作为示例
            print(f"前3行数据示例:")
            for row in range(1, min(4, max_row + 1)):
                row_data = []
                for col in range(1, min(max_col + 1, 11)):  # 只显示前10列
                    cell = worksheet.cell(row=row, column=col)
                    value = cell.value
                    if value is not None:
                        row_data.append(str(value)[:30])  # 限制显示长度
                    else:
                        row_data.append("")
                print(f"  行{row}: {row_data}")
        
        workbook.close()
        return template_info
        
    except Exception as e:
        print(f"分析Excel文件时出错: {e}")
        return None

def read_est_test_cases():
    """读取EST转换的测试用例"""
    csv_file = "EST测试用例.csv"
    
    if not os.path.exists(csv_file):
        print(f"EST测试用例文件不存在: {csv_file}")
        return []
    
    test_cases = []
    try:
        with open(csv_file, 'r', encoding='utf-8-sig') as f:
            reader = csv.DictReader(f)
            for row in reader:
                test_cases.append(row)
    except:
        try:
            with open(csv_file, 'r', encoding='gbk') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    test_cases.append(row)
        except Exception as e:
            print(f"读取EST测试用例失败: {e}")
            return []
    
    print(f"读取到 {len(test_cases)} 个EST测试用例")
    return test_cases

def map_est_to_template_format(est_cases, template_headers):
    """将EST测试用例映射到模板格式"""

    print(f"模板表头: {template_headers}")

    # 根据实际模板格式进行映射
    # 模板格式: ['测试用例ID', '系统需求ID', '需求描述', '前置条件', '测试环境', '测试步骤和期望结果', '文件路径', '测试结果', '备注']

    mapped_cases = []
    for i, case in enumerate(est_cases, 1):
        # 生成系统需求ID
        test_type = case.get('测试类型', '')
        seq_num = case.get('序号', i)
        try:
            seq_num = int(seq_num)
        except:
            seq_num = i

        if 'scenario' in test_type:
            req_id = f"SysReq_EST_Func_{seq_num:04d}"
        else:
            req_id = f"SysReq_EST_StateFlow_{seq_num:04d}"

        # 生成文件路径
        file_path = f"d4q_driving\\EST\\{case.get('文件名', '')}"

        # 组合测试步骤和期望结果
        steps = case.get('测试步骤', '')
        expected = case.get('预期结果', '')
        steps_and_expected = f"{steps}\n\n期望结果: {expected}" if steps and expected else (steps or expected)

        mapped_case = {
            '测试用例ID': case.get('测试用例编号', f"test_est_{i:04d}"),
            '系统需求ID': req_id,
            '需求描述': case.get('测试用例名称', '状态机逻辑跳转验证'),
            '前置条件': case.get('前置条件', 'HIL台架正常运行，系统无故障，相关ECU正常通信'),
            '测试环境': 'HIL仿真环境',
            '测试步骤和期望结果': steps_and_expected,
            '文件路径': file_path,
            '测试结果': '',
            '备注': case.get('备注', '')
        }
        mapped_cases.append(mapped_case)

    return mapped_cases, None

def write_to_excel_template(template_file, test_cases, target_sheet="EST"):
    """将测试用例写入Excel模板的EST工作表"""

    if not os.path.exists(template_file):
        print(f"模板文件不存在: {template_file}")
        return False

    try:
        # 打开模板文件
        workbook = openpyxl.load_workbook(template_file)

        # 选择EST工作表
        if target_sheet in workbook.sheetnames:
            worksheet = workbook[target_sheet]
        else:
            print(f"工作表 {target_sheet} 不存在")
            return False

        print(f"写入工作表: {worksheet.title}")

        # 获取表头
        headers = []
        for col in range(1, worksheet.max_column + 1):
            cell = worksheet.cell(row=1, column=col)
            if cell.value:
                headers.append(str(cell.value).strip())
            else:
                headers.append("")

        # 映射EST测试用例到模板格式
        mapped_cases, mapping = map_est_to_template_format(test_cases, headers)

        if not mapped_cases:
            print("没有可映射的测试用例")
            return False

        # 清除现有数据（保留表头）
        for row in range(2, worksheet.max_row + 1):
            for col in range(1, worksheet.max_column + 1):
                worksheet.cell(row=row, column=col).value = None

        # 写入数据
        for i, case in enumerate(mapped_cases):
            row_num = 2 + i  # 从第2行开始写入

            for col_num, header in enumerate(headers, 1):
                if header and header in case:
                    cell = worksheet.cell(row=row_num, column=col_num)
                    cell.value = case[header]

                    # 设置基本样式
                    cell.font = Font(name='宋体', size=10)
                    cell.alignment = Alignment(horizontal='left', vertical='top', wrap_text=True)
                    cell.border = Border(
                        left=Side(style='thin'),
                        right=Side(style='thin'),
                        top=Side(style='thin'),
                        bottom=Side(style='thin')
                    )

        # 保存文件
        output_file = f"D4Q_L2功能测试用例_EST更新_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx"
        workbook.save(output_file)
        workbook.close()

        print(f"已将 {len(mapped_cases)} 个测试用例写入Excel文件: {output_file}")
        return True

    except Exception as e:
        print(f"写入Excel文件时出错: {e}")
        return False

def main():
    print("=== EST测试用例写入Excel模板工具 ===")
    
    # 1. 分析原始模板
    print("\n1. 分析原始Excel模板...")
    template_info = analyze_original_template()
    
    if not template_info:
        print("无法分析模板文件")
        return
    
    # 2. 读取EST测试用例
    print("\n2. 读取EST测试用例...")
    est_cases = read_est_test_cases()
    
    if not est_cases:
        print("没有EST测试用例可处理")
        return
    
    # 3. 写入Excel模板
    print("\n3. 将EST测试用例写入Excel模板...")
    template_file = "D4Q_L2功能测试用例_20250725.xlsx"

    # 直接写入EST工作表
    success = write_to_excel_template(template_file, est_cases, "EST")
    
    if success:
        print("\n转换完成！")
        print("生成的文件可以直接用Excel打开编辑。")
    else:
        print("\n转换失败，请检查错误信息。")

if __name__ == "__main__":
    main()
