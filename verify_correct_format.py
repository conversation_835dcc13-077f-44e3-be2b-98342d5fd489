#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证生成的Excel文件格式是否正确
"""

import openpyxl
import os

def verify_excel_format():
    """验证Excel格式是否符合要求"""
    
    # 找到最新生成的Excel文件
    directory = r"d:\测试项目\D4Q\测试用例\test_case_generate"
    excel_files = [f for f in os.listdir(directory) if f.startswith("D4Q_L2功能测试用例_EST更新_正确格式_") and f.endswith(".xlsx")]
    
    if not excel_files:
        print("❌ 未找到正确格式的Excel文件")
        return
    
    # 选择最新的文件
    latest_file = max(excel_files, key=lambda x: os.path.getctime(os.path.join(directory, x)))
    excel_path = os.path.join(directory, latest_file)
    
    print(f"📁 验证文件: {latest_file}")
    
    # 打开Excel文件
    wb = openpyxl.load_workbook(excel_path)
    ws = wb['EST']
    
    print(f"\n📊 基本信息:")
    print(f"   工作表名称: {ws.title}")
    print(f"   总行数: {ws.max_row}")
    print(f"   数据行数: {ws.max_row - 1}")
    print(f"   列数: {ws.max_column}")
    
    # 检查表头
    print(f"\n📋 表头信息:")
    headers = []
    for col in range(1, ws.max_column + 1):
        header = ws.cell(row=1, column=col).value
        headers.append(header)
        print(f"   列{col}: {header}")
    
    # 检查前几个测试用例的格式
    print(f"\n🔍 格式验证 - 检查前3个测试用例:")
    
    for row in range(2, min(5, ws.max_row + 1)):
        test_case_name = ws.cell(row=row, column=2).value
        steps_content = ws.cell(row=row, column=7).value
        
        print(f"\n--- 测试用例 {row-1}: {test_case_name} ---")
        
        if steps_content:
            # 分析步骤格式
            lines = steps_content.split('\n')
            step_count = 0
            operation_count = 0
            expected_count = 0
            
            for line in lines:
                line = line.strip()
                if line.startswith('Step '):
                    step_count += 1
                    print(f"   ✓ {line}")
                elif line.startswith('操作:'):
                    operation_count += 1
                    print(f"   ✓ {line}")
                elif line.startswith('Expected '):
                    expected_count += 1
                    print(f"   ✓ {line}")
            
            print(f"   📈 统计: {step_count}个步骤, {operation_count}个操作, {expected_count}个期望结果")
            
            # 验证格式是否正确
            if step_count == operation_count == expected_count:
                print(f"   ✅ 格式正确: 步骤、操作、期望结果数量匹配")
            else:
                print(f"   ❌ 格式问题: 数量不匹配")
        else:
            print(f"   ❌ 无步骤内容")
    
    # 检查是否包含关键格式元素
    print(f"\n🎯 关键格式检查:")
    
    sample_content = ws.cell(row=2, column=7).value
    if sample_content:
        has_step_format = "Step " in sample_content
        has_operation_format = "操作:" in sample_content
        has_expected_format = "Expected " in sample_content
        
        print(f"   Step X: 格式 - {'✅' if has_step_format else '❌'}")
        print(f"   操作: 格式 - {'✅' if has_operation_format else '❌'}")
        print(f"   Expected X: 格式 - {'✅' if has_expected_format else '❌'}")
        
        if has_step_format and has_operation_format and has_expected_format:
            print(f"   🎉 格式完全符合要求！")
        else:
            print(f"   ⚠️ 格式可能需要调整")
    
    # 统计信息
    print(f"\n📊 整体统计:")
    total_steps = 0
    for row in range(2, ws.max_row + 1):
        steps_content = ws.cell(row=row, column=7).value
        if steps_content:
            step_count = steps_content.count('Step ')
            total_steps += step_count
    
    print(f"   总测试用例数: {ws.max_row - 1}")
    print(f"   总测试步骤数: {total_steps}")
    print(f"   平均每个用例步骤数: {total_steps / (ws.max_row - 1):.1f}")
    
    wb.close()
    
    return excel_path

if __name__ == "__main__":
    try:
        excel_path = verify_excel_format()
        print(f"\n✅ 验证完成！文件路径: {excel_path}")
    except Exception as e:
        print(f"❌ 验证过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
