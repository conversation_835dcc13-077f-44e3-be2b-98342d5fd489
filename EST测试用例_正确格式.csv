﻿测试用例名称,功能模块,测试场景,测试步骤和期望结果,步骤数量
test_SysReq_EST_Func_0037,est,standstill2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速15kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 6: 验证GearShiftPos信号等于5
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于4(Stand Still)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0038,est,standstill2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速15kph
        设置前方20m存在同速同向车辆
操作: 设置自车初始速度为15kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 6: 验证GearShiftPos信号等于5
Step 7: 判断EST是否处于activewarning状态
        设置前车减速刹停
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于4(Stand Still)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0039,est,standstill2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速15kph
        设置前方20m存在同速同向车辆
操作: 设置自车初始速度为15kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 6: 验证GearShiftPos信号等于5
Step 7: 判断EST是否处于activewarning状态
        设置前车减速刹停
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于4(Stand Still)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0040,est,standstill2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速30kph
操作: 设置自车初始速度为30kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 6: 验证GearShiftPos信号等于5
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于4(Stand Still)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0041,est,activerightlanechange2activestop,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速30kph
操作: 设置自车初始速度为30kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 6: 验证GearShiftPos信号等于5
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 10: 验证DTE_ESTSts信号等于3(Active Stop)
Step 11: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于4(Stand Still)
Step 12: 停止录制数据
操作: 停止数据录制
Expected 12: 系统响应正常，功能执行正常
Step 13: 台架状态复位
操作: 重置仿真环境
Expected 13: 系统响应正常，功能执行正常",13
test_SysReq_EST_Func_0042,est,activewarning2activedeceleration,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速30kph
操作: 设置自车初始速度为30kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 6: 验证GearShiftPos信号等于5
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 10: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 10: 验证DTE_ESTSts信号等于3(Active Stop)
Step 11: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于4(Stand Still)
Step 12: 停止录制数据
操作: 停止数据录制
Expected 12: 系统响应正常，功能执行正常
Step 13: 台架状态复位
操作: 重置仿真环境
Expected 13: 系统响应正常，功能执行正常",13
test_SysReq_EST_Func_0043,est,activerightlanechange2activestop,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速30kph
操作: 设置自车初始速度为30kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 6: 验证GearShiftPos信号等于5
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 10: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 12: 验证DTE_ESTSts信号等于3(Active Stop)
Step 13: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 13: 验证DTE_ESTSts信号等于4(Stand Still)
Step 14: 停止录制数据
操作: 停止数据录制
Expected 14: 系统响应正常，功能执行正常
Step 15: 台架状态复位
操作: 重置仿真环境
Expected 15: 系统响应正常，功能执行正常",15
test_SysReq_EST_Func_0044,est,standstill2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速30kph
操作: 设置自车初始速度为30kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 6: 验证GearShiftPos信号等于5
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于4(Stand Still)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0032_activedeceleration2activeemergencelanechange,est,activedeceleration2activeemergencelanechange,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置速度显示大于20kph
        判断EST是否处于activeemergencelanechange状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 9: 验证DisplayVehicleSpeed信号等于25
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0034_activedeceleration2activestop,est,activedeceleration2activestop,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置速度显示小于20kph
        判断EST是否处于activestop状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 9: 验证DisplayVehicleSpeed信号等于5
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0033_activeemergencelanechange2activestop,est,activeemergencelanechange2activestop,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 12: 验证DTE_ESTSts信号等于3(Active Stop)
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0029_activerightlanechange2activedeceleration,est,activerightlanechange2activedeceleration,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 10: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0030_activerightlanechange2activestop,est,activerightlanechange2activestop,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置速度显示小于20kph
        判断EST是否处于activestop状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 10: 验证DisplayVehicleSpeed信号等于5
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0028_activespeedadaption2activerightlanechange,est,activespeedadaption2activerightlanechange,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置速度显示大于20kph
        判断EST是否处于activerightlanechange状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 9: 验证DisplayVehicleSpeed信号等于25
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0030_activespeedadaption2activestop,est,activespeedadaption2activestop,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置速度显示小于20kph
        判断EST是否处于activestop状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 9: 验证DisplayVehicleSpeed信号等于5
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0027_activewarning2activedeceleration,est,activewarning2activedeceleration,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0026_activewarning2activespeedadaption,est,activewarning2activespeedadaption,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0025_activewarning2activestop,est,activewarning2activestop,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activedeceleration2passive_4L,est,activedeceleration2passive_4L,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_4LModeFed信号状态
Expected 9: 验证HCU_4LModeFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_ABS,est,activedeceleration2passive_ABS,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 激活ABS
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_ABSActive信号状态
Expected 9: 验证ABS_ESP_1_ABSActive信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_acl,est,activedeceleration2passive_acl,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置油门开度大于70%
        判断EST是否处于passive状态
操作: 验证VCU_1_G_AcceleratorPosRaw信号状态
Expected 9: 验证VCU_1_G_AcceleratorPosRaw信号等于75
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_AEB,est,activedeceleration2passive_AEB,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 激活AEB
        判断EST是否处于passive状态
操作: 设置DTE_AEBSettingSt信号为1
Expected 9: 系统响应正常，功能执行正常
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_brk,est,activedeceleration2passive_brk,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置刹车压力大于10bar
        判断EST是否处于passive状态
操作: 验证VCU_10_BrakePedalSts信号状态
Expected 9: 验证VCU_10_BrakePedalSts信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_CCO,est,activedeceleration2passive_CCO,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CCOSwtFed信号状态
Expected 9: 验证HCU_CCOSwtFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_clb,est,activedeceleration2passive_clb,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CLIMBFed信号状态
Expected 9: 验证HCU_CLIMBFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_curv,est,activedeceleration2passive_curv,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 进入曲率大于0.004的弯道
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于0(Off)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_drvhan,est,activedeceleration2passive_drvhan,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置驾驶员手力矩大于0.4Nm
        判断EST是否处于passive状态
操作: 验证TorsionBarTorque信号状态
Expected 9: 验证TorsionBarTorque信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_drvmod3,est,activedeceleration2passive_drvmod3,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_DriveMode_JT信号状态
Expected 9: 验证HCU_DriveMode_JT信号等于3
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_ESP,est,activedeceleration2passive_ESP,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 激活ESP
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_VDCActive信号状态
Expected 9: 验证ABS_ESP_1_VDCActive信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_ftgoff,est,activedeceleration2passive_ftgoff,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 关闭疲劳检测
        判断EST是否处于passive状态
操作: 验证ICC_DriveDrosinessLevel信号状态
Expected 9: 验证ICC_DriveDrosinessLevel信号等于0
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_gsft,est,activedeceleration2passive_gsft,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置上拨拨杆退出ACC
        判断EST是否处于passive状态
操作: 验证GearShiftPos信号状态
Expected 9: 验证GearShiftPos信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_lanlos,est,activedeceleration2passive_lanlos,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置车道线探测丢失
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于0(Off)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_spd140,est,activedeceleration2passive_spd140,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置速度大于135kph
        判断EST是否处于passive状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 9: 验证DisplayVehicleSpeed信号等于140
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activedeceleration2passive_trnsp,est,activedeceleration2passive_trnsp,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证FLZCU_CarMode信号状态
Expected 9: 验证FLZCU_CarMode信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_4L,est,activeemergencelanechange2passive_4L,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_4LModeFed信号状态
Expected 12: 验证HCU_4LModeFed信号等于1
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ABS,est,activeemergencelanechange2passive_ABS,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 激活ABS
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_ABSActive信号状态
Expected 12: 验证ABS_ESP_1_ABSActive信号等于1
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_acl,est,activeemergencelanechange2passive_acl,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置油门开度大于70%
        判断EST是否处于passive状态
操作: 验证VCU_1_G_AcceleratorPosRaw信号状态
Expected 12: 验证VCU_1_G_AcceleratorPosRaw信号等于75
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_AEB,est,activeemergencelanechange2passive_AEB,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 激活AEB
        判断EST是否处于passive状态
操作: 设置DTE_AEBSettingSt信号为1
Expected 12: 系统响应正常，功能执行正常
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_brk,est,activeemergencelanechange2passive_brk,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置刹车压力大于10bar
        判断EST是否处于passive状态
操作: 验证VCU_10_BrakePedalSts信号状态
Expected 12: 验证VCU_10_BrakePedalSts信号等于1
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_CCO,est,activeemergencelanechange2passive_CCO,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CCOSwtFed信号状态
Expected 12: 验证HCU_CCOSwtFed信号等于1
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_clb,est,activeemergencelanechange2passive_clb,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CLIMBFed信号状态
Expected 12: 验证HCU_CLIMBFed信号等于1
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_drvhan,est,activeemergencelanechange2passive_drvhan,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置驾驶员手力矩大于0.4Nm
        判断EST是否处于passive状态
操作: 验证TorsionBarTorque信号状态
Expected 12: 验证TorsionBarTorque信号等于2
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_drvmod3,est,activeemergencelanechange2passive_drvmod3,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_DriveMode_JT信号状态
Expected 12: 验证HCU_DriveMode_JT信号等于3
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ESP,est,activeemergencelanechange2passive_ESP,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 激活ESP
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_VDCActive信号状态
Expected 12: 验证ABS_ESP_1_VDCActive信号等于1
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ftgoff,est,activeemergencelanechange2passive_ftgoff,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 关闭疲劳检测
        判断EST是否处于passive状态
操作: 验证ICC_DriveDrosinessLevel信号状态
Expected 12: 验证ICC_DriveDrosinessLevel信号等于0
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_gsft,est,activeemergencelanechange2passive_gsft,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置上拨拨杆退出ACC
        判断EST是否处于passive状态
操作: 验证GearShiftPos信号状态
Expected 12: 验证GearShiftPos信号等于2
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_lanlos,est,activeemergencelanechange2passive_lanlos,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置车道线探测丢失
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 12: 验证DTE_ESTSts信号等于0(Off)
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_spd140,est,activeemergencelanechange2passive_spd140,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置速度大于135kph
        判断EST是否处于passive状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 12: 验证DisplayVehicleSpeed信号等于140
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activeemergencelanechange2passive_trnsp,est,activeemergencelanechange2passive_trnsp,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证FLZCU_CarMode信号状态
Expected 12: 验证FLZCU_CarMode信号等于2
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activerightlanechange2passive_4L,est,activerightlanechange2passive_4L,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_4LModeFed信号状态
Expected 10: 验证HCU_4LModeFed信号等于1
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_ABS,est,activerightlanechange2passive_ABS,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 激活ABS
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_ABSActive信号状态
Expected 10: 验证ABS_ESP_1_ABSActive信号等于1
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_acl,est,activerightlanechange2passive_acl,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置油门开度大于70%
        判断EST是否处于passive状态
操作: 验证VCU_1_G_AcceleratorPosRaw信号状态
Expected 10: 验证VCU_1_G_AcceleratorPosRaw信号等于75
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_AEB,est,activerightlanechange2passive_AEB,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 激活AEB
        判断EST是否处于passive状态
操作: 设置DTE_AEBSettingSt信号为1
Expected 10: 系统响应正常，功能执行正常
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_brk,est,activerightlanechange2passive_brk,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置刹车压力大于10bar
        判断EST是否处于passive状态
操作: 验证VCU_10_BrakePedalSts信号状态
Expected 10: 验证VCU_10_BrakePedalSts信号等于1
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_CCO,est,activerightlanechange2passive_CCO,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CCOSwtFed信号状态
Expected 10: 验证HCU_CCOSwtFed信号等于1
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_clb,est,activerightlanechange2passive_clb,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CLIMBFed信号状态
Expected 10: 验证HCU_CLIMBFed信号等于1
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_curv,est,activerightlanechange2passive_curv,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 进入曲率大于0.004的弯道
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 10: 验证DTE_ESTSts信号等于0(Off)
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_drvhan,est,activerightlanechange2passive_drvhan,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置驾驶员手力矩大于0.4Nm
        判断EST是否处于passive状态
操作: 验证TorsionBarTorque信号状态
Expected 10: 验证TorsionBarTorque信号等于2
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_drvmod3,est,activerightlanechange2passive_drvmod3,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_DriveMode_JT信号状态
Expected 10: 验证HCU_DriveMode_JT信号等于3
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_ESP,est,activerightlanechange2passive_ESP,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 激活ESP
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_VDCActive信号状态
Expected 10: 验证ABS_ESP_1_VDCActive信号等于1
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_ftgoff,est,activerightlanechange2passive_ftgoff,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 关闭疲劳检测
        判断EST是否处于passive状态
操作: 验证ICC_DriveDrosinessLevel信号状态
Expected 10: 验证ICC_DriveDrosinessLevel信号等于0
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_gsft,est,activerightlanechange2passive_gsft,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置上拨拨杆退出ACC
        判断EST是否处于passive状态
操作: 验证GearShiftPos信号状态
Expected 10: 验证GearShiftPos信号等于2
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_lanlos,est,activerightlanechange2passive_lanlos,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置车道线探测丢失
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 10: 验证DTE_ESTSts信号等于0(Off)
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_spd140,est,activerightlanechange2passive_spd140,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置速度大于135kph
        判断EST是否处于passive状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 10: 验证DisplayVehicleSpeed信号等于140
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activerightlanechange2passive_trnsp,est,activerightlanechange2passive_trnsp,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证FLZCU_CarMode信号状态
Expected 10: 验证FLZCU_CarMode信号等于2
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activespeedadaption2passive_4L,est,activespeedadaption2passive_4L,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_4LModeFed信号状态
Expected 9: 验证HCU_4LModeFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_ABS,est,activespeedadaption2passive_ABS,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 激活ABS
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_ABSActive信号状态
Expected 9: 验证ABS_ESP_1_ABSActive信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_acl,est,activespeedadaption2passive_acl,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置油门开度大于70%
        判断EST是否处于passive状态
操作: 验证VCU_1_G_AcceleratorPosRaw信号状态
Expected 9: 验证VCU_1_G_AcceleratorPosRaw信号等于75
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_AEB,est,activespeedadaption2passive_AEB,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 激活AEB
        判断EST是否处于passive状态
操作: 设置DTE_AEBSettingSt信号为1
Expected 9: 系统响应正常，功能执行正常
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_brk,est,activespeedadaption2passive_brk,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置刹车压力大于10bar
        判断EST是否处于passive状态
操作: 验证VCU_10_BrakePedalSts信号状态
Expected 9: 验证VCU_10_BrakePedalSts信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_CCO,est,activespeedadaption2passive_CCO,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CCOSwtFed信号状态
Expected 9: 验证HCU_CCOSwtFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_clb,est,activespeedadaption2passive_clb,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CLIMBFed信号状态
Expected 9: 验证HCU_CLIMBFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_curv,est,activespeedadaption2passive_curv,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 进入曲率大于0.004的弯道
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于0(Off)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_drvhan,est,activespeedadaption2passive_drvhan,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置驾驶员手力矩大于0.4Nm
        判断EST是否处于passive状态
操作: 验证TorsionBarTorque信号状态
Expected 9: 验证TorsionBarTorque信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_drvmod3,est,activespeedadaption2passive_drvmod3,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_DriveMode_JT信号状态
Expected 9: 验证HCU_DriveMode_JT信号等于3
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_ESP,est,activespeedadaption2passive_ESP,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 激活ESP
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_VDCActive信号状态
Expected 9: 验证ABS_ESP_1_VDCActive信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_ftgoff,est,activespeedadaption2passive_ftgoff,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 关闭疲劳检测
        判断EST是否处于passive状态
操作: 验证ICC_DriveDrosinessLevel信号状态
Expected 9: 验证ICC_DriveDrosinessLevel信号等于0
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_gsft,est,activespeedadaption2passive_gsft,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置上拨拨杆退出ACC
        判断EST是否处于passive状态
操作: 验证GearShiftPos信号状态
Expected 9: 验证GearShiftPos信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_lanlos,est,activespeedadaption2passive_lanlos,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置车道线探测丢失
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于0(Off)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_spd140,est,activespeedadaption2passive_spd140,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置速度大于135kph
        判断EST是否处于passive状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 9: 验证DisplayVehicleSpeed信号等于140
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activespeedadaption2passive_trnsp,est,activespeedadaption2passive_trnsp,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证FLZCU_CarMode信号状态
Expected 9: 验证FLZCU_CarMode信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_4L,est,activestop2passive_4L,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_4LModeFed信号状态
Expected 9: 验证HCU_4LModeFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_ABS,est,activestop2passive_ABS,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 激活ABS
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_ABSActive信号状态
Expected 9: 验证ABS_ESP_1_ABSActive信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_acl,est,activestop2passive_acl,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置油门开度大于70%
        判断EST是否处于passive状态
操作: 验证VCU_1_G_AcceleratorPosRaw信号状态
Expected 9: 验证VCU_1_G_AcceleratorPosRaw信号等于75
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_AEB,est,activestop2passive_AEB,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 激活AEB
        判断EST是否处于passive状态
操作: 设置DTE_AEBSettingSt信号为1
Expected 9: 系统响应正常，功能执行正常
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_brk,est,activestop2passive_brk,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置刹车压力大于10bar
        判断EST是否处于passive状态
操作: 验证VCU_10_BrakePedalSts信号状态
Expected 9: 验证VCU_10_BrakePedalSts信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_CCO,est,activestop2passive_CCO,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CCOSwtFed信号状态
Expected 9: 验证HCU_CCOSwtFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_clb,est,activestop2passive_clb,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CLIMBFed信号状态
Expected 9: 验证HCU_CLIMBFed信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_curv,est,activestop2passive_curv,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速15kph
操作: 设置自车初始速度为15kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 进入曲率大于0.004的弯道
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于0(Off)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_drvhan,est,activestop2passive_drvhan,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置驾驶员手力矩大于0.4Nm
        判断EST是否处于passive状态
操作: 验证TorsionBarTorque信号状态
Expected 9: 验证TorsionBarTorque信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_drvmod3,est,activestop2passive_drvmod3,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_DriveMode_JT信号状态
Expected 9: 验证HCU_DriveMode_JT信号等于3
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_ESP,est,activestop2passive_ESP,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 激活ESP
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_VDCActive信号状态
Expected 9: 验证ABS_ESP_1_VDCActive信号等于1
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_ftgoff,est,activestop2passive_ftgoff,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 关闭疲劳检测
        判断EST是否处于passive状态
操作: 验证ICC_DriveDrosinessLevel信号状态
Expected 9: 验证ICC_DriveDrosinessLevel信号等于0
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_gsft,est,activestop2passive_gsft,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置上拨拨杆退出ACC
        判断EST是否处于passive状态
操作: 验证GearShiftPos信号状态
Expected 9: 验证GearShiftPos信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_lanlos,est,activestop2passive_lanlos,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置车道线探测丢失
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于0(Off)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_spd140,est,activestop2passive_spd140,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置速度大于135kph
        判断EST是否处于passive状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 9: 验证DisplayVehicleSpeed信号等于140
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2passive_trnsp,est,activestop2passive_trnsp,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证FLZCU_CarMode信号状态
Expected 9: 验证FLZCU_CarMode信号等于2
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activewarning2passive_4L,est,activewarning2passive_4L,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_4LModeFed信号状态
Expected 8: 验证HCU_4LModeFed信号等于1
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_ABS,est,activewarning2passive_ABS,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 激活ABS
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_ABSActive信号状态
Expected 8: 验证ABS_ESP_1_ABSActive信号等于1
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_acl,est,activewarning2passive_acl,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置油门开度大于70%
        判断EST是否处于passive状态
操作: 验证VCU_1_G_AcceleratorPosRaw信号状态
Expected 8: 验证VCU_1_G_AcceleratorPosRaw信号等于75
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_AEB,est,activewarning2passive_AEB,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 激活AEB
        判断EST是否处于passive状态
操作: 设置DTE_AEBSettingSt信号为1
Expected 8: 系统响应正常，功能执行正常
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_brk,est,activewarning2passive_brk,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置刹车压力大于10bar
        判断EST是否处于passive状态
操作: 验证VCU_10_BrakePedalSts信号状态
Expected 8: 验证VCU_10_BrakePedalSts信号等于1
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_CCO,est,activewarning2passive_CCO,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CCOSwtFed信号状态
Expected 8: 验证HCU_CCOSwtFed信号等于1
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_clb,est,activewarning2passive_clb,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_CLIMBFed信号状态
Expected 8: 验证HCU_CLIMBFed信号等于1
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_curv,est,activewarning2passive_curv,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 进入曲率大于0.004的弯道
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于0(Off)
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_drvhan,est,activewarning2passive_drvhan,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置驾驶员手力矩大于0.4Nm
        判断EST是否处于passive状态
操作: 验证TorsionBarTorque信号状态
Expected 8: 验证TorsionBarTorque信号等于2
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_drvmod3,est,activewarning2passive_drvmod3,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证HCU_DriveMode_JT信号状态
Expected 8: 验证HCU_DriveMode_JT信号等于3
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_ESP,est,activewarning2passive_ESP,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 激活ESP
        判断EST是否处于passive状态
操作: 验证ABS_ESP_1_VDCActive信号状态
Expected 8: 验证ABS_ESP_1_VDCActive信号等于1
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_ftgoff,est,activewarning2passive_ftgoff,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 关闭疲劳检测
        判断EST是否处于passive状态
操作: 验证ICC_DriveDrosinessLevel信号状态
Expected 8: 验证ICC_DriveDrosinessLevel信号等于0
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_gsft,est,activewarning2passive_gsft,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置上拨拨杆退出ACC
        判断EST是否处于passive状态
操作: 验证GearShiftPos信号状态
Expected 8: 验证GearShiftPos信号等于2
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_lanlos,est,activewarning2passive_lanlos,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置车道线探测丢失
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于0(Off)
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_spd140,est,activewarning2passive_spd140,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置速度大于135kph
        判断EST是否处于passive状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 8: 验证DisplayVehicleSpeed信号等于140
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_activewarning2passive_trnsp,est,activewarning2passive_trnsp,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 设置特殊驾驶模式
        判断EST是否处于passive状态
操作: 验证FLZCU_CarMode信号状态
Expected 8: 验证FLZCU_CarMode信号等于2
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0035_activestop2standstill,est,activestop2standstill,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 设置车辆静止
        判断EST是否处于standstill状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 9: 验证DisplayVehicleSpeed信号等于0
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_passive2fail,est,passive2fail,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 注入故障0x600016-蓄电池供电电压低于工作阈值
        判断EST是否处于failure状态
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于9(Failure)
Step 7: 取消注入故障
        判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于1(Passive)
Step 8: 停止录制数据
操作: 停止数据录制
Expected 8: 系统响应正常，功能执行正常
Step 9: 台架状态复位
操作: 重置仿真环境
Expected 9: 系统响应正常，功能执行正常",9
test_SysReq_SysReq_EST_Func_0019_off2passive,est,off2passive,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 2: 系统响应正常，功能执行正常
Step 3: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 4: 系统响应正常，功能执行正常
Step 5: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 5: 系统响应正常，功能执行正常
Step 6: 打开EST功能
        判断EST是否处于passive状态
操作: 设置EST功能开关为1
Expected 6: 系统响应正常，功能执行正常
Step 7: 停止录制数据
操作: 停止数据录制
Expected 7: 系统响应正常，功能执行正常
Step 8: 台架状态复位
操作: 重置仿真环境
Expected 8: 系统响应正常，功能执行正常",8
test_SysReq_EST_Func_0020_activedeceleration2off,est,activedeceleration2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 9: 系统响应正常，功能执行正常
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0020_activeemergencelanechange2off,est,activeemergencelanechange2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 12: 系统响应正常，功能执行正常
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0020_activerightlanechange2off,est,activerightlanechange2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 10: 系统响应正常，功能执行正常
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0020_activespeedadaption2off,est,activespeedadaption2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 9: 系统响应正常，功能执行正常
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0020_activestop2off,est,activestop2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 9: 系统响应正常，功能执行正常
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0020_activewarning2off,est,activewarning2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 8: 系统响应正常，功能执行正常
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0020_passive2off,est,passive2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 2: 系统响应正常，功能执行正常
Step 3: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 4: 系统响应正常，功能执行正常
Step 5: 打开EST功能
        判断EST是否处于passive状态
操作: 设置EST功能开关为1
Expected 5: 系统响应正常，功能执行正常
Step 6: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 6: 系统响应正常，功能执行正常
Step 7: 停止录制数据
操作: 停止数据录制
Expected 7: 系统响应正常，功能执行正常
Step 8: 台架状态复位
操作: 重置仿真环境
Expected 8: 系统响应正常，功能执行正常",8
test_SysReq_EST_Func_0035_standstill2off,est,standstill2off,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activestop状态
        设置车辆静止
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于3(Active Stop)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于4(Stand Still)
Step 10: 关闭EST功能
        判断EST是否处于off状态
操作: 设置EST功能开关为2
Expected 10: 系统响应正常，功能执行正常
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0024_passive2activewarning,est,passive2activewarning,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于1(Passive)
Step 7: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 7: 验证GearShiftPos信号等于5
Step 8: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于2(Active Warning)
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0035_standstill2passive,est,standstill2passive,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activestop状态
        设置车辆静止
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于3(Active Stop)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于standstill状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于4(Stand Still)
Step 10: 设置车辆非静止
        判断EST是否处于passive状态
操作: 验证DisplayVehicleSpeed信号状态
Expected 10: 验证DisplayVehicleSpeed信号等于25
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activedeceleration2fail,est,activedeceleration2fail,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activedeceleration状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 注入故障0x600016-蓄电池供电电压低于工作阈值
        判断EST是否处于failure状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于9(Failure)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 取消注入故障
        台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activeemergencelanechange2fail,est,activeemergencelanechange2fail,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 8: 判断EST是否处于activedeceleration状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于7(Active Deceleration)
Step 9: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 9: 系统响应正常，功能执行正常
Step 10: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 10: 系统响应正常，功能执行正常
Step 11: 判断EST是否处于activeemergencelanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 11: 验证DTE_ESTSts信号等于8(Active EmergenceLaneChange)
Step 12: 注入故障0x600016-蓄电池供电电压低于工作阈值
        判断EST是否处于failure状态
操作: 验证DTE_ESTSts信号状态
Expected 12: 验证DTE_ESTSts信号等于9(Failure)
Step 13: 停止录制数据
操作: 停止数据录制
Expected 13: 系统响应正常，功能执行正常
Step 14: 取消注入故障
        台架状态复位
操作: 重置仿真环境
Expected 14: 系统响应正常，功能执行正常",14
test_SysReq_EST_Func_0023_activerightlanechange2fail,est,activerightlanechange2fail,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 判断EST是否处于activespeedadaption状态
        设置速度显示大于20kph
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 7: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 7: 系统响应正常，功能执行正常
Step 8: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 8: 系统响应正常，功能执行正常
Step 9: 判断EST是否处于activerightlanechange状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于6(Active RightLaneChange)
Step 10: 注入故障0x600016-蓄电池供电电压低于工作阈值
        判断EST是否处于failure状态
操作: 验证DTE_ESTSts信号状态
Expected 10: 验证DTE_ESTSts信号等于9(Failure)
Step 11: 停止录制数据
操作: 停止数据录制
Expected 11: 系统响应正常，功能执行正常
Step 12: 取消注入故障
        台架状态复位
操作: 重置仿真环境
Expected 12: 系统响应正常，功能执行正常",12
test_SysReq_EST_Func_0023_activespeedadaption2fail,est,activespeedadaption2fail,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activespeedadaption状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于5(Active SpeedAdaption)
Step 9: 注入故障0x600016-蓄电池供电电压低于工作阈值
        判断EST是否处于failure状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于9(Failure)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 取消注入故障
        台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activestop2fail,est,activestop2fail,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 5: 验证DTE_ESTSts信号等于2(Active Warning)
Step 6: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 6: 系统响应正常，功能执行正常
Step 7: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 7: 系统响应正常，功能执行正常
Step 8: 判断EST是否处于activestop状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于3(Active Stop)
Step 9: 注入故障0x600016-蓄电池供电电压低于工作阈值
        判断EST是否处于failure状态
操作: 验证DTE_ESTSts信号状态
Expected 9: 验证DTE_ESTSts信号等于9(Failure)
Step 10: 停止录制数据
操作: 停止数据录制
Expected 10: 系统响应正常，功能执行正常
Step 11: 取消注入故障
        台架状态复位
操作: 重置仿真环境
Expected 11: 系统响应正常，功能执行正常",11
test_SysReq_EST_Func_0023_activewarning2fail,est,activewarning2fail,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 激活ACC
        判断ACC是否处于active状态
        设置驾驶员重度疲劳
操作: 验证GearShiftPos信号状态
Expected 4: 验证GearShiftPos信号等于5
Step 5: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 5: 系统响应正常，功能执行正常
Step 6: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 6: 系统响应正常，功能执行正常
Step 7: 判断EST是否处于activewarning状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于2(Active Warning)
Step 8: 注入故障0x600016-蓄电池供电电压低于工作阈值
        判断EST是否处于failure状态
操作: 验证DTE_ESTSts信号状态
Expected 8: 验证DTE_ESTSts信号等于9(Failure)
Step 9: 停止录制数据
操作: 停止数据录制
Expected 9: 系统响应正常，功能执行正常
Step 10: 取消注入故障
        台架状态复位
操作: 重置仿真环境
Expected 10: 系统响应正常，功能执行正常",10
test_SysReq_EST_Func_0023_passive2fail,est,passive2fail,"Step 1: HIL台架初始化且无故障情况
		场景选择直道三车道
操作: 初始化仿真环境
Expected 1: 系统响应正常，功能执行正常
Step 2: 打开EST功能
操作: 设置EST功能开关为1
Expected 2: 系统响应正常，功能执行正常
Step 3: 设置自车车速25kph
操作: 设置自车初始速度为25kph
Expected 3: 系统响应正常，功能执行正常
Step 4: 配置项目工程路径
操作: 配置测试项目工程路径
Expected 4: 系统响应正常，功能执行正常
Step 5: 配置需要绘图信号
		录制cve数据
操作: 配置绘图信号并开始录制数据
Expected 5: 系统响应正常，功能执行正常
Step 6: 判断EST是否处于passive状态
操作: 验证DTE_ESTSts信号状态
Expected 6: 验证DTE_ESTSts信号等于1(Passive)
Step 7: 注入故障0x600016-蓄电池供电电压低于工作阈值
        判断EST是否处于failure状态
操作: 验证DTE_ESTSts信号状态
Expected 7: 验证DTE_ESTSts信号等于9(Failure)
Step 8: 停止录制数据
操作: 停止数据录制
Expected 8: 系统响应正常，功能执行正常
Step 9: 取消注入故障
        台架状态复位
操作: 重置仿真环境
Expected 9: 系统响应正常，功能执行正常",9
