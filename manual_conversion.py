#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re

def extract_test_info(file_path):
    """手动提取测试信息"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
    except:
        return None
    
    info = {
        'file': os.path.basename(file_path),
        'function': '',
        'feature': '',
        'story': '',
        'steps': []
    }
    
    # 提取函数名
    func_match = re.search(r'def (test_\w+)', content)
    if func_match:
        info['function'] = func_match.group(1)
    
    # 提取feature
    feature_match = re.search(r'@allure\.feature\("([^"]+)"\)', content)
    if feature_match:
        info['feature'] = feature_match.group(1)
    
    # 提取story
    story_match = re.search(r'@allure\.story\("([^"]+)"\)', content)
    if story_match:
        info['story'] = story_match.group(1)
    
    # 提取步骤
    steps = re.findall(r'with allure\.step\(\s*"""(.*?)"""\s*\):', content, re.DOTALL)
    for step in steps:
        clean_step = re.sub(r'\s+', ' ', step.strip())
        info['steps'].append(clean_step)
    
    return info

# 手动处理几个文件作为示例
test_files = [
    "EST/scenario/test_SysReq_EST_Func_0037.py",
    "EST/scenario/test_SysReq_EST_Func_0038.py",
    "EST/stateflow/active2active/test_actwrn2actstp.py"
]

print("=== EST脚本分析结果 ===\n")

for file_path in test_files:
    if os.path.exists(file_path):
        info = extract_test_info(file_path)
        if info:
            print(f"文件: {info['file']}")
            print(f"函数: {info['function']}")
            print(f"功能: {info['feature']}")
            print(f"故事: {info['story']}")
            print(f"步骤数: {len(info['steps'])}")
            for i, step in enumerate(info['steps'], 1):
                print(f"  {i}. {step[:100]}...")
            print("-" * 50)

print("分析完成！")
