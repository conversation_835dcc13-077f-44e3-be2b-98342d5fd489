#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确格式的EST脚本转换器
按照用户截图显示的Excel模板格式进行转换
"""

import os
import re
import json
import pandas as pd
from pathlib import Path

def parse_est_script_correct_format(file_path):
    """按照正确格式解析EST脚本文件"""
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 提取测试用例名称
    test_name_match = re.search(r'def (test_\w+)', content)
    test_name = test_name_match.group(1) if test_name_match else os.path.basename(file_path).replace('.py', '')
    
    # 提取allure.feature和allure.story
    feature_match = re.search(r'@allure\.feature\("([^"]+)"\)', content)
    story_match = re.search(r'@allure\.story\("([^"]+)"\)', content)
    
    feature = feature_match.group(1) if feature_match else ""
    story = story_match.group(1) if story_match else ""
    
    # 提取所有allure.step块及其代码
    step_pattern = r'with allure\.step\(\s*"""(.*?)"""\s*\):(.*?)(?=with allure\.step|def |$)'
    steps = re.findall(step_pattern, content, re.DOTALL)
    
    # 按照正确格式处理步骤
    formatted_steps = []
    
    for i, (step_desc, step_code) in enumerate(steps, 1):
        # 清理步骤描述
        step_desc = step_desc.strip()
        step_code = step_code.strip()
        
        # 分析代码以推断操作和期望结果
        operation, expected_result = analyze_step_code_detailed(step_code, step_desc)
        
        # 按照Excel模板格式组织：Step X: 描述
        step_text = f"Step {i}: {step_desc}"
        formatted_steps.append(step_text)
        
        # 操作: 具体操作
        operation_text = f"操作: {operation}"
        formatted_steps.append(operation_text)
        
        # Expected X: 期望结果
        expected_text = f"Expected {i}: {expected_result}"
        formatted_steps.append(expected_text)
    
    test_case = {
        'test_name': test_name,
        'feature': feature,
        'story': story,
        'steps': '\n'.join(formatted_steps),
        'step_count': len(steps)
    }
    
    return test_case

def analyze_step_code_detailed(step_code, step_desc):
    """详细分析步骤代码，提取操作和期望结果"""
    operation = "执行测试操作"
    expected_result = "系统响应正常，功能执行正常"
    
    # 根据代码内容分析具体操作
    if 'init_sim' in step_code:
        # 提取道路信息
        road_match = re.search(r'road_index=(\d+)', step_code)
        if road_match:
            road_index = road_match.group(1)
            operation = f"初始化仿真环境，选择道路{road_index}"
        else:
            operation = "初始化仿真环境"
        expected_result = "系统响应正常，功能执行正常"
        
    elif 'someip_signal_set' in step_code:
        # 提取信号设置信息
        signal_match = re.search(r'signal_name="([^"]+)"', step_code)
        value_match = re.search(r'signal_value=(\d+)', step_code)
        if signal_match:
            signal_name = signal_match.group(1)
            signal_value = value_match.group(1) if value_match else "指定值"
            if 'ESASettingSt' in signal_name:
                operation = f"设置EST功能开关为{signal_value}"
            else:
                operation = f"设置{signal_name}信号为{signal_value}"
        else:
            operation = "设置信号值"
        expected_result = "系统响应正常，功能执行正常"
        
    elif 'set_ego_init_state' in step_code:
        # 提取车速信息
        speed_match = re.search(r'set_ego_init_state\((\d+)', step_code)
        if speed_match:
            speed = speed_match.group(1)
            operation = f"设置自车初始速度为{speed}kph"
        else:
            operation = "设置自车初始状态"
        expected_result = "系统响应正常，功能执行正常"
        
    elif 'create_catalogue' in step_code:
        operation = "配置测试项目工程路径"
        expected_result = "系统响应正常，功能执行正常"
        
    elif 'set_plot_signals' in step_code or 'start_cve_record' in step_code:
        operation = "配置绘图信号并开始录制数据"
        expected_result = "系统响应正常，功能执行正常"
        
    elif 'meet_someip_value' in step_code:
        # 提取验证信息
        signal_match = re.search(r'signal_name="([^"]+)"', step_code)
        target_match = re.search(r'target_value=(\d+)', step_code)
        
        if signal_match and target_match:
            signal_name = signal_match.group(1)
            target_value = target_match.group(1)
            
            operation = f"验证{signal_name}信号状态"
            
            # 根据信号名称和值推断具体期望
            if 'ESTSts' in signal_name:
                status_map = {
                    '0': 'Off',
                    '1': 'Passive', 
                    '2': 'Active Warning',
                    '3': 'Active Stop',
                    '4': 'Stand Still',
                    '5': 'Active SpeedAdaption',
                    '6': 'Active RightLaneChange',
                    '7': 'Active Deceleration',
                    '8': 'Active EmergenceLaneChange',
                    '9': 'Failure'
                }
                status_name = status_map.get(target_value, f'状态{target_value}')
                expected_result = f"验证{signal_name}信号等于{target_value}({status_name})"
            elif 'ACCSts' in signal_name:
                expected_result = f"验证{signal_name}信号等于{target_value}"
            else:
                expected_result = f"验证{signal_name}信号等于{target_value}"
        else:
            operation = "验证信号状态"
            expected_result = "信号状态符合预期"
            
    elif 'set_signal_value' in step_code:
        # 提取信号设置信息
        signal_match = re.search(r'signal_name="([^"]+)"', step_code)
        value_match = re.search(r'target_value=(\d+)', step_code)
        if signal_match:
            signal_name = signal_match.group(1)
            signal_value = value_match.group(1) if value_match else "指定值"
            if 'GearShiftPos' in signal_name:
                operation = f"设置档位信号为{signal_value}"
            elif 'DrosinessLevel' in signal_name:
                operation = f"设置驾驶员疲劳等级为{signal_value}"
            else:
                operation = f"设置{signal_name}信号为{signal_value}"
        else:
            operation = "设置信号值"
        expected_result = "系统响应正常，功能执行正常"
        
    elif 'stop_plot_data' in step_code or 'stop_record' in step_code:
        operation = "停止数据录制"
        expected_result = "系统响应正常，功能执行正常"
        
    elif 'reset_sim' in step_code:
        operation = "重置仿真环境"
        expected_result = "系统响应正常，功能执行正常"
        
    elif 'npc' in step_code:
        operation = "配置NPC车辆"
        expected_result = "系统响应正常，功能执行正常"
        
    else:
        # 尝试从步骤描述中提取操作信息
        if step_desc:
            operation = step_desc
        expected_result = "系统响应正常，功能执行正常"
    
    return operation, expected_result

def process_est_directory_correct(est_dir):
    """处理EST脚本目录，按正确格式转换所有脚本"""
    test_cases = []
    
    # 处理scenario目录
    scenario_dir = os.path.join(est_dir, 'scenario')
    if os.path.exists(scenario_dir):
        for file_name in os.listdir(scenario_dir):
            if file_name.endswith('.py') and file_name.startswith('test_'):
                file_path = os.path.join(scenario_dir, file_name)
                try:
                    test_case = parse_est_script_correct_format(file_path)
                    test_cases.append(test_case)
                    print(f"已处理: {file_name}")
                except Exception as e:
                    print(f"处理文件 {file_name} 时出错: {e}")
    
    # 处理stateflow目录
    stateflow_dir = os.path.join(est_dir, 'stateflow')
    if os.path.exists(stateflow_dir):
        for subdir in os.listdir(stateflow_dir):
            subdir_path = os.path.join(stateflow_dir, subdir)
            if os.path.isdir(subdir_path):
                for file_name in os.listdir(subdir_path):
                    if file_name.endswith('.py') and file_name.startswith('test_'):
                        file_path = os.path.join(subdir_path, file_name)
                        try:
                            test_case = parse_est_script_correct_format(file_path)
                            test_cases.append(test_case)
                            print(f"已处理: {subdir}/{file_name}")
                        except Exception as e:
                            print(f"处理文件 {subdir}/{file_name} 时出错: {e}")
    
    return test_cases

def save_correct_format(test_cases, output_dir):
    """保存为正确格式"""
    # 保存为CSV
    csv_data = []
    for case in test_cases:
        csv_data.append({
            '测试用例名称': case['test_name'],
            '功能模块': case['feature'],
            '测试场景': case['story'],
            '测试步骤和期望结果': case['steps'],
            '步骤数量': case['step_count']
        })
    
    df = pd.DataFrame(csv_data)
    csv_path = os.path.join(output_dir, 'EST测试用例_正确格式.csv')
    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
    
    print(f"正确格式转换完成！")
    print(f"CSV文件: {csv_path}")
    print(f"总计处理了 {len(test_cases)} 个测试用例")
    
    return csv_path

if __name__ == "__main__":
    # 设置路径
    est_directory = r"d:\测试项目\D4Q\测试用例\test_case_generate\EST"
    output_directory = r"d:\测试项目\D4Q\测试用例\test_case_generate"
    
    # 确保输出目录存在
    os.makedirs(output_directory, exist_ok=True)
    
    # 处理EST脚本
    test_cases = process_est_directory_correct(est_directory)
    
    # 保存结果
    csv_path = save_correct_format(test_cases, output_directory)
