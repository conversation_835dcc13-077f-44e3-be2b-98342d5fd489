#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分析Excel模板文件结构的脚本
"""

import pandas as pd
import openpyxl
import os
import sys

def analyze_excel_template(file_path):
    """分析Excel模板文件的结构"""
    print(f"正在分析Excel文件: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"文件不存在: {file_path}")
        return
    
    try:
        # 使用openpyxl读取Excel文件
        workbook = openpyxl.load_workbook(file_path, data_only=False)
        
        print(f"\n工作表列表:")
        for i, sheet_name in enumerate(workbook.sheetnames):
            print(f"  {i+1}. {sheet_name}")
        
        # 分析每个工作表
        for sheet_name in workbook.sheetnames:
            print(f"\n=== 工作表: {sheet_name} ===")
            worksheet = workbook[sheet_name]
            
            # 获取工作表的尺寸
            max_row = worksheet.max_row
            max_col = worksheet.max_column
            print(f"最大行数: {max_row}, 最大列数: {max_col}")
            
            # 读取前10行的内容作为示例
            print(f"\n前10行内容:")
            for row in range(1, min(11, max_row + 1)):
                row_data = []
                for col in range(1, min(11, max_col + 1)):  # 只显示前10列
                    cell = worksheet.cell(row=row, column=col)
                    value = cell.value
                    if value is not None:
                        row_data.append(str(value)[:50])  # 限制显示长度
                    else:
                        row_data.append("")
                
                if any(row_data):  # 只显示非空行
                    print(f"  行{row}: {row_data}")
            
            # 查找表头信息
            print(f"\n查找可能的表头信息:")
            for row in range(1, min(6, max_row + 1)):
                for col in range(1, min(11, max_col + 1)):
                    cell = worksheet.cell(row=row, column=col)
                    if cell.value and isinstance(cell.value, str):
                        value = str(cell.value).strip()
                        if any(keyword in value for keyword in ['测试', '用例', '编号', 'ID', '名称', '步骤', '预期', '结果']):
                            print(f"    行{row}列{col}: {value}")
        
        workbook.close()
        
    except Exception as e:
        print(f"读取Excel文件时出错: {e}")
        
        # 尝试使用pandas读取
        try:
            print("\n尝试使用pandas读取...")
            excel_file = pd.ExcelFile(file_path)
            print(f"工作表列表: {excel_file.sheet_names}")
            
            for sheet_name in excel_file.sheet_names:
                print(f"\n=== 工作表: {sheet_name} ===")
                df = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
                print(f"形状: {df.shape}")
                print("前5行:")
                print(df.head())
                
        except Exception as e2:
            print(f"使用pandas读取也失败: {e2}")

if __name__ == "__main__":
    template_file = "D4Q_L2功能测试用例_20250725.xlsx"
    analyze_excel_template(template_file)
