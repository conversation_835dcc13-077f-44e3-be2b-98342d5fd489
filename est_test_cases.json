[{"序号": 1, "测试用例编号": "test_SysReq_EST_Func_0037", "测试用例名称": "standstill2off", "功能模块": "est", "测试类型": "scenario", "文件名": "test_SysReq_EST_Func_0037.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速15kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activestop状态\n9. 判断EST是否处于standstill状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 2, "测试用例编号": "test_SysReq_EST_Func_0038", "测试用例名称": "standstill2off", "功能模块": "est", "测试类型": "scenario", "文件名": "test_SysReq_EST_Func_0038.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速15kph 设置前方20m存在同速同向车辆\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n7. 判断EST是否处于activewarning状态 设置前车减速刹停\n8. 判断EST是否处于activestop状态\n9. 判断EST是否处于standstill状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 3, "测试用例编号": "test_SysReq_EST_Func_0039", "测试用例名称": "standstill2off", "功能模块": "est", "测试类型": "scenario", "文件名": "test_SysReq_EST_Func_0039.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速15kph 设置前方20m存在同速同向车辆\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n7. 判断EST是否处于activewarning状态 设置前车减速刹停\n8. 判断EST是否处于activestop状态\n9. 判断EST是否处于standstill状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 4, "测试用例编号": "test_SysReq_EST_Func_0040", "测试用例名称": "standstill2off", "功能模块": "est", "测试类型": "scenario", "文件名": "test_SysReq_EST_Func_0040.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速30kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activestop状态\n9. 判断EST是否处于standstill状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 5, "测试用例编号": "test_SysReq_EST_Func_0041", "测试用例名称": "activerightlanechange2activestop", "功能模块": "est", "测试类型": "scenario", "文件名": "test_SysReq_EST_Func_0041.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速30kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activespeedadaption状态\n9. 判断EST是否处于activerightlanechange状态\n10. 判断EST是否处于activestop状态\n11. 判断EST是否处于standstill状态\n12. 停止录制数据\n13. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Stop -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 6, "测试用例编号": "test_SysReq_EST_Func_0042", "测试用例名称": "activewarning2activedeceleration", "功能模块": "est", "测试类型": "scenario", "文件名": "test_SysReq_EST_Func_0042.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速30kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activedeceleration状态\n9. 判断EST是否处于activeemergencelanechange状态\n10. 判断EST是否处于activestop状态\n11. 判断EST是否处于standstill状态\n12. 停止录制数据\n13. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Active EmergenceLaneChange -> Active Stop -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 7, "测试用例编号": "test_SysReq_EST_Func_0043", "测试用例名称": "activerightlanechange2activestop", "功能模块": "est", "测试类型": "scenario", "文件名": "test_SysReq_EST_Func_0043.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速30kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activespeedadaption状态\n9. 判断EST是否处于activerightlanechange状态\n10. 判断EST是否处于activedeceleration状态\n11. 判断EST是否处于activeemergencelanechange状态\n12. 判断EST是否处于activestop状态\n13. 判断EST是否处于standstill状态\n14. 停止录制数据\n15. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Active Stop -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 8, "测试用例编号": "test_SysReq_EST_Func_0044", "测试用例名称": "standstill2off", "功能模块": "est", "测试类型": "scenario", "文件名": "test_SysReq_EST_Func_0044.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速30kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activestop状态\n9. 判断EST是否处于standstill状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 9, "测试用例编号": "test_SysReq_EST_Func_0032_activedeceleration2activeemergencelanechange", "测试用例名称": "activedeceleration2activeemergencelanechange", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actdec2actELC.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置速度显示大于20kph 判断EST是否处于activeemergencelanechange状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Active EmergenceLaneChange", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 10, "测试用例编号": "test_SysReq_EST_Func_0034_activedeceleration2activestop", "测试用例名称": "activedeceleration2activestop", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actdec2actstp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置速度显示小于20kph 判断EST是否处于activestop状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Active SpeedAdaption -> Active Stop", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 11, "测试用例编号": "test_SysReq_EST_Func_0033_activeemergencelanechange2activestop", "测试用例名称": "activeemergencelanechange2activestop", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actELC2actstp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 判断EST是否处于activestop状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Active Stop", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 12, "测试用例编号": "test_SysReq_EST_Func_0029_activerightlanechange2activedeceleration", "测试用例名称": "activerightlanechange2activedeceleration", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actRLC2actdec.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 判断EST是否处于activedeceleration状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 13, "测试用例编号": "test_SysReq_EST_Func_0030_activerightlanechange2activestop", "测试用例名称": "activerightlanechange2activestop", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actRLC2actstp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置速度显示小于20kph 判断EST是否处于activestop状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active SpeedAdaption -> Active Stop", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 14, "测试用例编号": "test_SysReq_EST_Func_0028_activespeedadaption2activerightlanechange", "测试用例名称": "activespeedadaption2activerightlanechange", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actspdadp2actRLC.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置速度显示大于20kph 判断EST是否处于activerightlanechange状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 15, "测试用例编号": "test_SysReq_EST_Func_0030_activespeedadaption2activestop", "测试用例名称": "activespeedadaption2activestop", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actspdadp2actstp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置速度显示小于20kph 判断EST是否处于activestop状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active SpeedAdaption -> Active Stop", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 16, "测试用例编号": "test_SysReq_EST_Func_0027_activewarning2activedeceleration", "测试用例名称": "activewarning2activedeceleration", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actwrn2actdec.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activedeceleration状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 17, "测试用例编号": "test_SysReq_EST_Func_0026_activewarning2activespeedadaption", "测试用例名称": "activewarning2activespeedadaption", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actwrn2actspdadp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activespeedadaption状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 18, "测试用例编号": "test_SysReq_EST_Func_0025_activewarning2activestop", "测试用例名称": "activewarning2activestop", "功能模块": "est", "测试类型": "stateflow/active2active", "文件名": "test_actwrn2actstp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 判断EST是否处于activestop状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 19, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_4L", "测试用例名称": "activedeceleration2passive_4L", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_4L.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 20, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_ABS", "测试用例名称": "activedeceleration2passive_ABS", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_ABS.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 激活ABS 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 21, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_acl", "测试用例名称": "activedeceleration2passive_acl", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_acl.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置油门开度大于70% 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 22, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_AEB", "测试用例名称": "activedeceleration2passive_AEB", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_AEB.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 激活AEB 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Stand Still -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 23, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_brk", "测试用例名称": "activedeceleration2passive_brk", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_brk.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置刹车压力大于10bar 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 24, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_CCO", "测试用例名称": "activedeceleration2passive_CCO", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_CCO.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 25, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_clb", "测试用例名称": "activedeceleration2passive_clb", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_clb.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 26, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_curv", "测试用例名称": "activedeceleration2passive_curv", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_curv.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 27, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_drvhan", "测试用例名称": "activedeceleration2passive_drvhan", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_drvhan.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 28, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_drvmod3", "测试用例名称": "activedeceleration2passive_drvmod3", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_drvmod3.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 29, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_ESP", "测试用例名称": "activedeceleration2passive_ESP", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_ESP.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 激活ESP 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 30, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_ftgoff", "测试用例名称": "activedeceleration2passive_ftgoff", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_ftgoff.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 关闭疲劳检测 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Off -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 31, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_gsft", "测试用例名称": "activedeceleration2passive_gsft", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_gsft.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 32, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_lanlos", "测试用例名称": "activedeceleration2passive_lanlos", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_lanlos.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置车道线探测丢失 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 33, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_spd140", "测试用例名称": "activedeceleration2passive_spd140", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_spd140.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置速度大于135kph 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 34, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2passive_trnsp", "测试用例名称": "activedeceleration2passive_trnsp", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actdec2psv_trnsp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 35, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_4L", "测试用例名称": "activeemergencelanechange2passive_4L", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_4L.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置特殊驾驶模式 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 36, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ABS", "测试用例名称": "activeemergencelanechange2passive_ABS", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_ABS.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 激活ABS 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 37, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_acl", "测试用例名称": "activeemergencelanechange2passive_acl", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_acl.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置油门开度大于70% 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 38, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_AEB", "测试用例名称": "activeemergencelanechange2passive_AEB", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_AEB.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 激活AEB 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Stand Still -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 39, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_brk", "测试用例名称": "activeemergencelanechange2passive_brk", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_brk.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置刹车压力大于10bar 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 40, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_CCO", "测试用例名称": "activeemergencelanechange2passive_CCO", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_CCO.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置特殊驾驶模式 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 41, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_clb", "测试用例名称": "activeemergencelanechange2passive_clb", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_clb.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置特殊驾驶模式 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 42, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_drvhan", "测试用例名称": "activeemergencelanechange2passive_drvhan", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_drvhan.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 43, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_drvmod3", "测试用例名称": "activeemergencelanechange2passive_drvmod3", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_drvmod3.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置特殊驾驶模式 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 44, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ESP", "测试用例名称": "activeemergencelanechange2passive_ESP", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_ESP.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 激活ESP 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 45, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ftgoff", "测试用例名称": "activeemergencelanechange2passive_ftgoff", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_ftgoff.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 关闭疲劳检测 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Off -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 46, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_gsft", "测试用例名称": "activeemergencelanechange2passive_gsft", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_gsft.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置上拨拨杆退出ACC 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 47, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_lanlos", "测试用例名称": "activeemergencelanechange2passive_lanlos", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_lanlos.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置车道线探测丢失 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 48, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_spd140", "测试用例名称": "activeemergencelanechange2passive_spd140", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_spd140.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置速度大于135kph 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 49, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2passive_trnsp", "测试用例名称": "activeemergencelanechange2passive_trnsp", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actELC2psv_trnsp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 设置特殊驾驶模式 判断EST是否处于passive状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 50, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_4L", "测试用例名称": "activerightlanechange2passive_4L", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_4L.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置特殊驾驶模式 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 51, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_ABS", "测试用例名称": "activerightlanechange2passive_ABS", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_ABS.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 激活ABS 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 52, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_acl", "测试用例名称": "activerightlanechange2passive_acl", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_acl.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置油门开度大于70% 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 53, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_AEB", "测试用例名称": "activerightlanechange2passive_AEB", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_AEB.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 激活AEB 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Stand Still -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 54, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_brk", "测试用例名称": "activerightlanechange2passive_brk", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_brk.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置刹车压力大于10bar 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 55, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_CCO", "测试用例名称": "activerightlanechange2passive_CCO", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_CCO.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置特殊驾驶模式 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 56, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_clb", "测试用例名称": "activerightlanechange2passive_clb", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_clb.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置特殊驾驶模式 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 57, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_curv", "测试用例名称": "activerightlanechange2passive_curv", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_curv.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 进入曲率大于0.004的弯道 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 58, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_drvhan", "测试用例名称": "activerightlanechange2passive_drvhan", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_drvhan.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 59, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_drvmod3", "测试用例名称": "activerightlanechange2passive_drvmod3", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_drvmod3.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置特殊驾驶模式 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 60, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_ESP", "测试用例名称": "activerightlanechange2passive_ESP", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_ESP.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 激活ESP 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 61, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_ftgoff", "测试用例名称": "activerightlanechange2passive_ftgoff", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_ftgoff.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 关闭疲劳检测 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Off -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 62, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_gsft", "测试用例名称": "activerightlanechange2passive_gsft", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_gsft.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置上拨拨杆退出ACC 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 63, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_lanlos", "测试用例名称": "activerightlanechange2passive_lanlos", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_lanlos.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置车道线探测丢失 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 64, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_spd140", "测试用例名称": "activerightlanechange2passive_spd140", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_spd140.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置速度大于135kph 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 65, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2passive_trnsp", "测试用例名称": "activerightlanechange2passive_trnsp", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actRLC2psv_trnsp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 设置特殊驾驶模式 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 66, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_4L", "测试用例名称": "activespeedadaption2passive_4L", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_4L.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 67, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_ABS", "测试用例名称": "activespeedadaption2passive_ABS", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_ABS.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 激活ABS 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 68, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_acl", "测试用例名称": "activespeedadaption2passive_acl", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_acl.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置油门开度大于70% 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 69, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_AEB", "测试用例名称": "activespeedadaption2passive_AEB", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_AEB.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 激活AEB 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Stand Still -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 70, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_brk", "测试用例名称": "activespeedadaption2passive_brk", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_brk.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置刹车压力大于10bar 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 71, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_CCO", "测试用例名称": "activespeedadaption2passive_CCO", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_CCO.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 72, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_clb", "测试用例名称": "activespeedadaption2passive_clb", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_clb.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 73, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_curv", "测试用例名称": "activespeedadaption2passive_curv", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_curv.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 74, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_drvhan", "测试用例名称": "activespeedadaption2passive_drvhan", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_drvhan.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 75, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_drvmod3", "测试用例名称": "activespeedadaption2passive_drvmod3", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_drvmod3.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 76, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_ESP", "测试用例名称": "activespeedadaption2passive_ESP", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_ESP.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 激活ESP 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 77, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_ftgoff", "测试用例名称": "activespeedadaption2passive_ftgoff", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_ftgoff.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 关闭疲劳检测 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Off -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 78, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_gsft", "测试用例名称": "activespeedadaption2passive_gsft", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_gsft.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 79, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_lanlos", "测试用例名称": "activespeedadaption2passive_lanlos", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_lanlos.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置车道线探测丢失 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 80, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_spd140", "测试用例名称": "activespeedadaption2passive_spd140", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_spd140.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置速度大于135kph 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 81, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2passive_trnsp", "测试用例名称": "activespeedadaption2passive_trnsp", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actspdadp2psv_trnsp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 82, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_4L", "测试用例名称": "activestop2passive_4L", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_4L.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 83, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_ABS", "测试用例名称": "activestop2passive_ABS", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_ABS.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 激活ABS 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 84, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_acl", "测试用例名称": "activestop2passive_acl", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_acl.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置油门开度大于70% 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 85, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_AEB", "测试用例名称": "activestop2passive_AEB", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_AEB.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 激活AEB 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Stand Still -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 86, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_brk", "测试用例名称": "activestop2passive_brk", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_brk.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置刹车压力大于10bar 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 87, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_CCO", "测试用例名称": "activestop2passive_CCO", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_CCO.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 88, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_clb", "测试用例名称": "activestop2passive_clb", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_clb.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 89, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_curv", "测试用例名称": "activestop2passive_curv", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_curv.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速15kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 90, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_drvhan", "测试用例名称": "activestop2passive_drvhan", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_drvhan.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 91, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_drvmod3", "测试用例名称": "activestop2passive_drvmod3", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_drvmod3.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 92, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_ESP", "测试用例名称": "activestop2passive_ESP", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_ESP.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 激活ESP 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 93, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_ftgoff", "测试用例名称": "activestop2passive_ftgoff", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_ftgoff.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 关闭疲劳检测 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 94, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_gsft", "测试用例名称": "activestop2passive_gsft", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_gsft.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 95, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_lanlos", "测试用例名称": "activestop2passive_lanlos", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_lanlos.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置车道线探测丢失 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 96, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_spd140", "测试用例名称": "activestop2passive_spd140", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_spd140.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置速度大于135kph 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 97, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2passive_trnsp", "测试用例名称": "activestop2passive_trnsp", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actstp2psv_trnsp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置特殊驾驶模式 判断EST是否处于passive状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 98, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_4L", "测试用例名称": "activewarning2passive_4L", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_4L.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置特殊驾驶模式 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 99, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_ABS", "测试用例名称": "activewarning2passive_ABS", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_ABS.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 激活ABS 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 100, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_acl", "测试用例名称": "activewarning2passive_acl", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_acl.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置油门开度大于70% 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 101, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_AEB", "测试用例名称": "activewarning2passive_AEB", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_AEB.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 激活AEB 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Stand Still -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 102, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_brk", "测试用例名称": "activewarning2passive_brk", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_brk.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置刹车压力大于10bar 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 103, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_CCO", "测试用例名称": "activewarning2passive_CCO", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_CCO.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置特殊驾驶模式 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 104, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_clb", "测试用例名称": "activewarning2passive_clb", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_clb.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置特殊驾驶模式 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 105, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_curv", "测试用例名称": "activewarning2passive_curv", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_curv.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 进入曲率大于0.004的弯道 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 106, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_drvhan", "测试用例名称": "activewarning2passive_drvhan", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_drvhan.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 107, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_drvmod3", "测试用例名称": "activewarning2passive_drvmod3", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_drvmod3.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置特殊驾驶模式 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 108, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_ESP", "测试用例名称": "activewarning2passive_ESP", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_ESP.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 激活ESP 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 109, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_ftgoff", "测试用例名称": "activewarning2passive_ftgoff", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_ftgoff.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 关闭疲劳检测 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Off -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 110, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_gsft", "测试用例名称": "activewarning2passive_gsft", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_gsft.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置上拨拨杆退出ACC 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 111, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_lanlos", "测试用例名称": "activewarning2passive_lanlos", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_lanlos.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置车道线探测丢失 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 112, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_spd140", "测试用例名称": "activewarning2passive_spd140", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_spd140.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置速度大于135kph 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 113, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2passive_trnsp", "测试用例名称": "activewarning2passive_trnsp", "功能模块": "est", "测试类型": "stateflow/active2passive", "文件名": "test_actwrn2psv_trnsp.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 设置特殊驾驶模式 判断EST是否处于passive状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 114, "测试用例编号": "test_SysReq_EST_Func_0035_activestop2standstill", "测试用例名称": "activestop2standstill", "功能模块": "est", "测试类型": "stateflow/active2standstill", "文件名": "test_actstp2stdstill.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 设置车辆静止 判断EST是否处于standstill状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off -> Stand Still", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 115, "测试用例编号": "test_SysReq_EST_Func_0023_passive2fail", "测试用例名称": "passive2fail", "功能模块": "est", "测试类型": "stateflow/fail2passive", "文件名": "test_fail2psv.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态\n7. 取消注入故障 判断EST是否处于passive状态\n8. 停止录制数据\n9. 台架状态复位", "预期结果": "EST状态转换: Failure -> Passive", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 116, "测试用例编号": "test_SysReq_SysReq_EST_Func_0019_off2passive", "测试用例名称": "off2passive", "功能模块": "est", "测试类型": "stateflow/off2passive", "文件名": "test_off2passive.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 设置自车车速25kph\n3. 配置项目工程路径\n4. 配置需要绘图信号 录制cve数据\n5. 关闭EST功能 判断EST是否处于off状态\n6. 打开EST功能 判断EST是否处于passive状态\n7. 停止录制数据\n8. 台架状态复位", "预期结果": "EST状态转换: Off -> Passive", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 117, "测试用例编号": "test_SysReq_EST_Func_0020_activedeceleration2off", "测试用例名称": "activedeceleration2off", "功能模块": "est", "测试类型": "stateflow/on2off", "文件名": "test_actdec2off.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 关闭EST功能 判断EST是否处于off状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 118, "测试用例编号": "test_SysReq_EST_Func_0020_activeemergencelanechange2off", "测试用例名称": "activeemergencelanechange2off", "功能模块": "est", "测试类型": "stateflow/on2off", "文件名": "test_actELC2off.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 关闭EST功能 判断EST是否处于off状态\n13. 停止录制数据\n14. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 119, "测试用例编号": "test_SysReq_EST_Func_0020_activerightlanechange2off", "测试用例名称": "activerightlanechange2off", "功能模块": "est", "测试类型": "stateflow/on2off", "文件名": "test_actRLC2off.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 关闭EST功能 判断EST是否处于off状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 120, "测试用例编号": "test_SysReq_EST_Func_0020_activespeedadaption2off", "测试用例名称": "activespeedadaption2off", "功能模块": "est", "测试类型": "stateflow/on2off", "文件名": "test_actspdadp2off.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 关闭EST功能 判断EST是否处于off状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 121, "测试用例编号": "test_SysReq_EST_Func_0020_activestop2off", "测试用例名称": "activestop2off", "功能模块": "est", "测试类型": "stateflow/on2off", "文件名": "test_actstp2off.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 关闭EST功能 判断EST是否处于off状态\n10. 停止录制数据\n11. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 122, "测试用例编号": "test_SysReq_EST_Func_0020_activewarning2off", "测试用例名称": "activewarning2off", "功能模块": "est", "测试类型": "stateflow/on2off", "文件名": "test_actwrn2off.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 关闭EST功能 判断EST是否处于off状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 123, "测试用例编号": "test_SysReq_EST_Func_0020_passive2off", "测试用例名称": "passive2off", "功能模块": "est", "测试类型": "stateflow/on2off", "文件名": "test_passive2off.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 设置自车车速25kph\n3. 配置项目工程路径\n4. 配置需要绘图信号 录制cve数据\n5. 打开EST功能 判断EST是否处于passive状态\n6. 关闭EST功能 判断EST是否处于off状态\n7. 停止录制数据\n8. 台架状态复位", "预期结果": "EST状态转换: Passive -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 124, "测试用例编号": "test_SysReq_EST_Func_0035_standstill2off", "测试用例名称": "standstill2off", "功能模块": "est", "测试类型": "stateflow/on2off", "文件名": "test_stdstl2off.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activestop状态 设置车辆静止\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于standstill状态\n10. 关闭EST功能 判断EST是否处于off状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off -> Stand Still -> Off", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 125, "测试用例编号": "test_SysReq_EST_Func_0024_passive2activewarning", "测试用例名称": "passive2activewarning", "功能模块": "est", "测试类型": "stateflow/passive2actve_warning", "文件名": "test_psv2actwrn.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 判断EST是否处于passive状态\n7. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n8. 判断EST是否处于activewarning状态\n9. 停止录制数据\n10. 台架状态复位", "预期结果": "EST状态转换: Passive -> Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 126, "测试用例编号": "test_SysReq_EST_Func_0035_standstill2passive", "测试用例名称": "standstill2passive", "功能模块": "est", "测试类型": "stateflow/standstill2passive", "文件名": "test_stdstl2passive.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activestop状态 设置车辆静止\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于standstill状态\n10. 设置车辆非静止 判断EST是否处于passive状态\n11. 停止录制数据\n12. 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Off -> Stand Still -> Passive", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 127, "测试用例编号": "test_SysReq_EST_Func_0023_activedeceleration2fail", "测试用例名称": "activedeceleration2fail", "功能模块": "est", "测试类型": "stateflow/state2failure", "文件名": "test_actdec2fail.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activedeceleration状态\n9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态\n10. 停止录制数据\n11. 取消注入故障 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Deceleration -> Failure", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 128, "测试用例编号": "test_SysReq_EST_Func_0023_activeemergencelanechange2fail", "测试用例名称": "activeemergencelanechange2fail", "功能模块": "est", "测试类型": "stateflow/state2failure", "文件名": "test_actELC2fail.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 判断EST是否处于activerightlanechange状态\n8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph\n9. 配置项目工程路径\n10. 配置需要绘图信号 录制cve数据\n11. 判断EST是否处于activeemergencelanechange状态\n12. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态\n13. 停止录制数据\n14. 取消注入故障 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Active Deceleration -> Active EmergenceLaneChange -> Failure", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 129, "测试用例编号": "test_SysReq_EST_Func_0023_activerightlanechange2fail", "测试用例名称": "activerightlanechange2fail", "功能模块": "est", "测试类型": "stateflow/state2failure", "文件名": "test_actRLC2fail.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph\n7. 配置项目工程路径\n8. 配置需要绘图信号 录制cve数据\n9. 判断EST是否处于activerightlanechange状态\n10. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态\n11. 停止录制数据\n12. 取消注入故障 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Active RightLaneChange -> Failure", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 130, "测试用例编号": "test_SysReq_EST_Func_0023_activespeedadaption2fail", "测试用例名称": "activespeedadaption2fail", "功能模块": "est", "测试类型": "stateflow/state2failure", "文件名": "test_actspdadp2fail.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activespeedadaption状态\n9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态\n10. 停止录制数据\n11. 取消注入故障 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active SpeedAdaption -> Failure", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 131, "测试用例编号": "test_SysReq_EST_Func_0023_activestop2fail", "测试用例名称": "activestop2fail", "功能模块": "est", "测试类型": "stateflow/state2failure", "文件名": "test_actstp2fail.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 判断EST是否处于activewarning状态\n6. 配置项目工程路径\n7. 配置需要绘图信号 录制cve数据\n8. 判断EST是否处于activestop状态\n9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态\n10. 停止录制数据\n11. 取消注入故障 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Active Stop -> Failure", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 132, "测试用例编号": "test_SysReq_EST_Func_0023_activewarning2fail", "测试用例名称": "activewarning2fail", "功能模块": "est", "测试类型": "stateflow/state2failure", "文件名": "test_actwrn2fail.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳\n5. 配置项目工程路径\n6. 配置需要绘图信号 录制cve数据\n7. 判断EST是否处于activewarning状态\n8. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态\n9. 停止录制数据\n10. 取消注入故障 台架状态复位", "预期结果": "EST状态转换: Active SpeedAdaption -> Active Stop -> Active SpeedAdaption -> Active Warning -> Failure", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}, {"序号": 133, "测试用例编号": "test_SysReq_EST_Func_0023_passive2fail", "测试用例名称": "passive2fail", "功能模块": "est", "测试类型": "stateflow/state2failure", "文件名": "test_psv2fail.py", "前置条件": "HIL台架初始化且无故障情况", "测试步骤": "1. HIL台架初始化且无故障情况 场景选择直道三车道\n2. 打开EST功能\n3. 设置自车车速25kph\n4. 配置项目工程路径\n5. 配置需要绘图信号 录制cve数据\n6. 判断EST是否处于passive状态\n7. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态\n8. 停止录制数据\n9. 取消注入故障 台架状态复位", "预期结果": "EST状态转换: Passive -> Failure", "测试数据": "监控信号: DTE_ESTSts, DisplayVehicleSpeed, ADS_EPS_1_SteerPilotAgEna, ADS_1_PilotParkBrkDecTar", "备注": ""}]