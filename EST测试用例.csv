﻿序号,测试用例编号,测试用例名称,功能模块,测试类型,文件名,前置条件,测试步骤,预期结果,测试数据,备注
1,test_SysReq_EST_Func_0037,standstill2off,est,scenario,test_SysReq_EST_Func_0037.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速15kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activestop状态
9. 判断EST是否处于standstill状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
2,test_SysReq_EST_Func_0038,standstill2off,est,scenario,test_SysReq_EST_Func_0038.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速15kph 设置前方20m存在同速同向车辆
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
7. 判断EST是否处于activewarning状态 设置前车减速刹停
8. 判断EST是否处于activestop状态
9. 判断EST是否处于standstill状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
3,test_SysReq_EST_Func_0039,standstill2off,est,scenario,test_SysReq_EST_Func_0039.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速15kph 设置前方20m存在同速同向车辆
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
7. 判断EST是否处于activewarning状态 设置前车减速刹停
8. 判断EST是否处于activestop状态
9. 判断EST是否处于standstill状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
4,test_SysReq_EST_Func_0040,standstill2off,est,scenario,test_SysReq_EST_Func_0040.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速30kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activestop状态
9. 判断EST是否处于standstill状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
5,test_SysReq_EST_Func_0041,activerightlanechange2activestop,est,scenario,test_SysReq_EST_Func_0041.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速30kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activespeedadaption状态
9. 判断EST是否处于activerightlanechange状态
10. 判断EST是否处于activestop状态
11. 判断EST是否处于standstill状态
12. 停止录制数据
13. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
6,test_SysReq_EST_Func_0042,activewarning2activedeceleration,est,scenario,test_SysReq_EST_Func_0042.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速30kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activedeceleration状态
9. 判断EST是否处于activeemergencelanechange状态
10. 判断EST是否处于activestop状态
11. 判断EST是否处于standstill状态
12. 停止录制数据
13. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
7,test_SysReq_EST_Func_0043,activerightlanechange2activestop,est,scenario,test_SysReq_EST_Func_0043.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速30kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activespeedadaption状态
9. 判断EST是否处于activerightlanechange状态
10. 判断EST是否处于activedeceleration状态
11. 判断EST是否处于activeemergencelanechange状态
12. 判断EST是否处于activestop状态
13. 判断EST是否处于standstill状态
14. 停止录制数据
15. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
8,test_SysReq_EST_Func_0044,standstill2off,est,scenario,test_SysReq_EST_Func_0044.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速30kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activestop状态
9. 判断EST是否处于standstill状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
9,test_SysReq_EST_Func_0032_activedeceleration2activeemergencelanechange,activedeceleration2activeemergencelanechange,est,stateflow/active2active,test_actdec2actELC.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置速度显示大于20kph 判断EST是否处于activeemergencelanechange状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
10,test_SysReq_EST_Func_0034_activedeceleration2activestop,activedeceleration2activestop,est,stateflow/active2active,test_actdec2actstp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置速度显示小于20kph 判断EST是否处于activestop状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
11,test_SysReq_EST_Func_0033_activeemergencelanechange2activestop,activeemergencelanechange2activestop,est,stateflow/active2active,test_actELC2actstp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 判断EST是否处于activestop状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
12,test_SysReq_EST_Func_0029_activerightlanechange2activedeceleration,activerightlanechange2activedeceleration,est,stateflow/active2active,test_actRLC2actdec.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 判断EST是否处于activedeceleration状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
13,test_SysReq_EST_Func_0030_activerightlanechange2activestop,activerightlanechange2activestop,est,stateflow/active2active,test_actRLC2actstp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置速度显示小于20kph 判断EST是否处于activestop状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
14,test_SysReq_EST_Func_0028_activespeedadaption2activerightlanechange,activespeedadaption2activerightlanechange,est,stateflow/active2active,test_actspdadp2actRLC.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置速度显示大于20kph 判断EST是否处于activerightlanechange状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
15,test_SysReq_EST_Func_0030_activespeedadaption2activestop,activespeedadaption2activestop,est,stateflow/active2active,test_actspdadp2actstp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置速度显示小于20kph 判断EST是否处于activestop状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
16,test_SysReq_EST_Func_0027_activewarning2activedeceleration,activewarning2activedeceleration,est,stateflow/active2active,test_actwrn2actdec.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activedeceleration状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
17,test_SysReq_EST_Func_0026_activewarning2activespeedadaption,activewarning2activespeedadaption,est,stateflow/active2active,test_actwrn2actspdadp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activespeedadaption状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
18,test_SysReq_EST_Func_0025_activewarning2activestop,activewarning2activestop,est,stateflow/active2active,test_actwrn2actstp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 判断EST是否处于activestop状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
19,test_SysReq_EST_Func_0023_activedeceleration2passive_4L,activedeceleration2passive_4L,est,stateflow/active2passive,test_actdec2psv_4L.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
20,test_SysReq_EST_Func_0023_activedeceleration2passive_ABS,activedeceleration2passive_ABS,est,stateflow/active2passive,test_actdec2psv_ABS.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 激活ABS 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
21,test_SysReq_EST_Func_0023_activedeceleration2passive_acl,activedeceleration2passive_acl,est,stateflow/active2passive,test_actdec2psv_acl.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置油门开度大于70% 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
22,test_SysReq_EST_Func_0023_activedeceleration2passive_AEB,activedeceleration2passive_AEB,est,stateflow/active2passive,test_actdec2psv_AEB.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 激活AEB 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
23,test_SysReq_EST_Func_0023_activedeceleration2passive_brk,activedeceleration2passive_brk,est,stateflow/active2passive,test_actdec2psv_brk.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置刹车压力大于10bar 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
24,test_SysReq_EST_Func_0023_activedeceleration2passive_CCO,activedeceleration2passive_CCO,est,stateflow/active2passive,test_actdec2psv_CCO.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
25,test_SysReq_EST_Func_0023_activedeceleration2passive_clb,activedeceleration2passive_clb,est,stateflow/active2passive,test_actdec2psv_clb.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
26,test_SysReq_EST_Func_0023_activedeceleration2passive_curv,activedeceleration2passive_curv,est,stateflow/active2passive,test_actdec2psv_curv.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
27,test_SysReq_EST_Func_0023_activedeceleration2passive_drvhan,activedeceleration2passive_drvhan,est,stateflow/active2passive,test_actdec2psv_drvhan.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
28,test_SysReq_EST_Func_0023_activedeceleration2passive_drvmod3,activedeceleration2passive_drvmod3,est,stateflow/active2passive,test_actdec2psv_drvmod3.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
29,test_SysReq_EST_Func_0023_activedeceleration2passive_ESP,activedeceleration2passive_ESP,est,stateflow/active2passive,test_actdec2psv_ESP.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 激活ESP 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
30,test_SysReq_EST_Func_0023_activedeceleration2passive_ftgoff,activedeceleration2passive_ftgoff,est,stateflow/active2passive,test_actdec2psv_ftgoff.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 关闭疲劳检测 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
31,test_SysReq_EST_Func_0023_activedeceleration2passive_gsft,activedeceleration2passive_gsft,est,stateflow/active2passive,test_actdec2psv_gsft.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
32,test_SysReq_EST_Func_0023_activedeceleration2passive_lanlos,activedeceleration2passive_lanlos,est,stateflow/active2passive,test_actdec2psv_lanlos.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置车道线探测丢失 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
33,test_SysReq_EST_Func_0023_activedeceleration2passive_spd140,activedeceleration2passive_spd140,est,stateflow/active2passive,test_actdec2psv_spd140.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置速度大于135kph 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
34,test_SysReq_EST_Func_0023_activedeceleration2passive_trnsp,activedeceleration2passive_trnsp,est,stateflow/active2passive,test_actdec2psv_trnsp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
35,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_4L,activeemergencelanechange2passive_4L,est,stateflow/active2passive,test_actELC2psv_4L.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置特殊驾驶模式 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
36,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ABS,activeemergencelanechange2passive_ABS,est,stateflow/active2passive,test_actELC2psv_ABS.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 激活ABS 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
37,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_acl,activeemergencelanechange2passive_acl,est,stateflow/active2passive,test_actELC2psv_acl.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置油门开度大于70% 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
38,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_AEB,activeemergencelanechange2passive_AEB,est,stateflow/active2passive,test_actELC2psv_AEB.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 激活AEB 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
39,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_brk,activeemergencelanechange2passive_brk,est,stateflow/active2passive,test_actELC2psv_brk.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置刹车压力大于10bar 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
40,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_CCO,activeemergencelanechange2passive_CCO,est,stateflow/active2passive,test_actELC2psv_CCO.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置特殊驾驶模式 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
41,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_clb,activeemergencelanechange2passive_clb,est,stateflow/active2passive,test_actELC2psv_clb.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置特殊驾驶模式 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
42,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_drvhan,activeemergencelanechange2passive_drvhan,est,stateflow/active2passive,test_actELC2psv_drvhan.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
43,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_drvmod3,activeemergencelanechange2passive_drvmod3,est,stateflow/active2passive,test_actELC2psv_drvmod3.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置特殊驾驶模式 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
44,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ESP,activeemergencelanechange2passive_ESP,est,stateflow/active2passive,test_actELC2psv_ESP.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 激活ESP 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
45,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_ftgoff,activeemergencelanechange2passive_ftgoff,est,stateflow/active2passive,test_actELC2psv_ftgoff.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 关闭疲劳检测 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
46,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_gsft,activeemergencelanechange2passive_gsft,est,stateflow/active2passive,test_actELC2psv_gsft.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置上拨拨杆退出ACC 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
47,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_lanlos,activeemergencelanechange2passive_lanlos,est,stateflow/active2passive,test_actELC2psv_lanlos.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置车道线探测丢失 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
48,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_spd140,activeemergencelanechange2passive_spd140,est,stateflow/active2passive,test_actELC2psv_spd140.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置速度大于135kph 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
49,test_SysReq_EST_Func_0023_activeemergencelanechange2passive_trnsp,activeemergencelanechange2passive_trnsp,est,stateflow/active2passive,test_actELC2psv_trnsp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 设置特殊驾驶模式 判断EST是否处于passive状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
50,test_SysReq_EST_Func_0023_activerightlanechange2passive_4L,activerightlanechange2passive_4L,est,stateflow/active2passive,test_actRLC2psv_4L.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置特殊驾驶模式 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
51,test_SysReq_EST_Func_0023_activerightlanechange2passive_ABS,activerightlanechange2passive_ABS,est,stateflow/active2passive,test_actRLC2psv_ABS.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 激活ABS 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
52,test_SysReq_EST_Func_0023_activerightlanechange2passive_acl,activerightlanechange2passive_acl,est,stateflow/active2passive,test_actRLC2psv_acl.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置油门开度大于70% 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
53,test_SysReq_EST_Func_0023_activerightlanechange2passive_AEB,activerightlanechange2passive_AEB,est,stateflow/active2passive,test_actRLC2psv_AEB.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 激活AEB 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
54,test_SysReq_EST_Func_0023_activerightlanechange2passive_brk,activerightlanechange2passive_brk,est,stateflow/active2passive,test_actRLC2psv_brk.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置刹车压力大于10bar 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
55,test_SysReq_EST_Func_0023_activerightlanechange2passive_CCO,activerightlanechange2passive_CCO,est,stateflow/active2passive,test_actRLC2psv_CCO.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置特殊驾驶模式 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
56,test_SysReq_EST_Func_0023_activerightlanechange2passive_clb,activerightlanechange2passive_clb,est,stateflow/active2passive,test_actRLC2psv_clb.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置特殊驾驶模式 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
57,test_SysReq_EST_Func_0023_activerightlanechange2passive_curv,activerightlanechange2passive_curv,est,stateflow/active2passive,test_actRLC2psv_curv.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 进入曲率大于0.004的弯道 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
58,test_SysReq_EST_Func_0023_activerightlanechange2passive_drvhan,activerightlanechange2passive_drvhan,est,stateflow/active2passive,test_actRLC2psv_drvhan.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
59,test_SysReq_EST_Func_0023_activerightlanechange2passive_drvmod3,activerightlanechange2passive_drvmod3,est,stateflow/active2passive,test_actRLC2psv_drvmod3.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置特殊驾驶模式 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
60,test_SysReq_EST_Func_0023_activerightlanechange2passive_ESP,activerightlanechange2passive_ESP,est,stateflow/active2passive,test_actRLC2psv_ESP.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 激活ESP 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
61,test_SysReq_EST_Func_0023_activerightlanechange2passive_ftgoff,activerightlanechange2passive_ftgoff,est,stateflow/active2passive,test_actRLC2psv_ftgoff.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 关闭疲劳检测 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
62,test_SysReq_EST_Func_0023_activerightlanechange2passive_gsft,activerightlanechange2passive_gsft,est,stateflow/active2passive,test_actRLC2psv_gsft.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置上拨拨杆退出ACC 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
63,test_SysReq_EST_Func_0023_activerightlanechange2passive_lanlos,activerightlanechange2passive_lanlos,est,stateflow/active2passive,test_actRLC2psv_lanlos.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置车道线探测丢失 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
64,test_SysReq_EST_Func_0023_activerightlanechange2passive_spd140,activerightlanechange2passive_spd140,est,stateflow/active2passive,test_actRLC2psv_spd140.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置速度大于135kph 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
65,test_SysReq_EST_Func_0023_activerightlanechange2passive_trnsp,activerightlanechange2passive_trnsp,est,stateflow/active2passive,test_actRLC2psv_trnsp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 设置特殊驾驶模式 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
66,test_SysReq_EST_Func_0023_activespeedadaption2passive_4L,activespeedadaption2passive_4L,est,stateflow/active2passive,test_actspdadp2psv_4L.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
67,test_SysReq_EST_Func_0023_activespeedadaption2passive_ABS,activespeedadaption2passive_ABS,est,stateflow/active2passive,test_actspdadp2psv_ABS.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 激活ABS 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
68,test_SysReq_EST_Func_0023_activespeedadaption2passive_acl,activespeedadaption2passive_acl,est,stateflow/active2passive,test_actspdadp2psv_acl.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置油门开度大于70% 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
69,test_SysReq_EST_Func_0023_activespeedadaption2passive_AEB,activespeedadaption2passive_AEB,est,stateflow/active2passive,test_actspdadp2psv_AEB.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 激活AEB 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
70,test_SysReq_EST_Func_0023_activespeedadaption2passive_brk,activespeedadaption2passive_brk,est,stateflow/active2passive,test_actspdadp2psv_brk.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置刹车压力大于10bar 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
71,test_SysReq_EST_Func_0023_activespeedadaption2passive_CCO,activespeedadaption2passive_CCO,est,stateflow/active2passive,test_actspdadp2psv_CCO.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
72,test_SysReq_EST_Func_0023_activespeedadaption2passive_clb,activespeedadaption2passive_clb,est,stateflow/active2passive,test_actspdadp2psv_clb.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
73,test_SysReq_EST_Func_0023_activespeedadaption2passive_curv,activespeedadaption2passive_curv,est,stateflow/active2passive,test_actspdadp2psv_curv.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
74,test_SysReq_EST_Func_0023_activespeedadaption2passive_drvhan,activespeedadaption2passive_drvhan,est,stateflow/active2passive,test_actspdadp2psv_drvhan.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
75,test_SysReq_EST_Func_0023_activespeedadaption2passive_drvmod3,activespeedadaption2passive_drvmod3,est,stateflow/active2passive,test_actspdadp2psv_drvmod3.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
76,test_SysReq_EST_Func_0023_activespeedadaption2passive_ESP,activespeedadaption2passive_ESP,est,stateflow/active2passive,test_actspdadp2psv_ESP.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 激活ESP 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
77,test_SysReq_EST_Func_0023_activespeedadaption2passive_ftgoff,activespeedadaption2passive_ftgoff,est,stateflow/active2passive,test_actspdadp2psv_ftgoff.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 关闭疲劳检测 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
78,test_SysReq_EST_Func_0023_activespeedadaption2passive_gsft,activespeedadaption2passive_gsft,est,stateflow/active2passive,test_actspdadp2psv_gsft.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
79,test_SysReq_EST_Func_0023_activespeedadaption2passive_lanlos,activespeedadaption2passive_lanlos,est,stateflow/active2passive,test_actspdadp2psv_lanlos.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置车道线探测丢失 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
80,test_SysReq_EST_Func_0023_activespeedadaption2passive_spd140,activespeedadaption2passive_spd140,est,stateflow/active2passive,test_actspdadp2psv_spd140.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置速度大于135kph 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
81,test_SysReq_EST_Func_0023_activespeedadaption2passive_trnsp,activespeedadaption2passive_trnsp,est,stateflow/active2passive,test_actspdadp2psv_trnsp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
82,test_SysReq_EST_Func_0023_activestop2passive_4L,activestop2passive_4L,est,stateflow/active2passive,test_actstp2psv_4L.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
83,test_SysReq_EST_Func_0023_activestop2passive_ABS,activestop2passive_ABS,est,stateflow/active2passive,test_actstp2psv_ABS.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 激活ABS 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
84,test_SysReq_EST_Func_0023_activestop2passive_acl,activestop2passive_acl,est,stateflow/active2passive,test_actstp2psv_acl.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置油门开度大于70% 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
85,test_SysReq_EST_Func_0023_activestop2passive_AEB,activestop2passive_AEB,est,stateflow/active2passive,test_actstp2psv_AEB.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 激活AEB 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
86,test_SysReq_EST_Func_0023_activestop2passive_brk,activestop2passive_brk,est,stateflow/active2passive,test_actstp2psv_brk.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置刹车压力大于10bar 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
87,test_SysReq_EST_Func_0023_activestop2passive_CCO,activestop2passive_CCO,est,stateflow/active2passive,test_actstp2psv_CCO.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
88,test_SysReq_EST_Func_0023_activestop2passive_clb,activestop2passive_clb,est,stateflow/active2passive,test_actstp2psv_clb.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
89,test_SysReq_EST_Func_0023_activestop2passive_curv,activestop2passive_curv,est,stateflow/active2passive,test_actstp2psv_curv.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速15kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 进入曲率大于0.004的弯道 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
90,test_SysReq_EST_Func_0023_activestop2passive_drvhan,activestop2passive_drvhan,est,stateflow/active2passive,test_actstp2psv_drvhan.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
91,test_SysReq_EST_Func_0023_activestop2passive_drvmod3,activestop2passive_drvmod3,est,stateflow/active2passive,test_actstp2psv_drvmod3.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
92,test_SysReq_EST_Func_0023_activestop2passive_ESP,activestop2passive_ESP,est,stateflow/active2passive,test_actstp2psv_ESP.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 激活ESP 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
93,test_SysReq_EST_Func_0023_activestop2passive_ftgoff,activestop2passive_ftgoff,est,stateflow/active2passive,test_actstp2psv_ftgoff.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 关闭疲劳检测 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
94,test_SysReq_EST_Func_0023_activestop2passive_gsft,activestop2passive_gsft,est,stateflow/active2passive,test_actstp2psv_gsft.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置上拨拨杆退出ACC 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
95,test_SysReq_EST_Func_0023_activestop2passive_lanlos,activestop2passive_lanlos,est,stateflow/active2passive,test_actstp2psv_lanlos.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置车道线探测丢失 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
96,test_SysReq_EST_Func_0023_activestop2passive_spd140,activestop2passive_spd140,est,stateflow/active2passive,test_actstp2psv_spd140.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置速度大于135kph 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
97,test_SysReq_EST_Func_0023_activestop2passive_trnsp,activestop2passive_trnsp,est,stateflow/active2passive,test_actstp2psv_trnsp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置特殊驾驶模式 判断EST是否处于passive状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
98,test_SysReq_EST_Func_0023_activewarning2passive_4L,activewarning2passive_4L,est,stateflow/active2passive,test_actwrn2psv_4L.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置特殊驾驶模式 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
99,test_SysReq_EST_Func_0023_activewarning2passive_ABS,activewarning2passive_ABS,est,stateflow/active2passive,test_actwrn2psv_ABS.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 激活ABS 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
100,test_SysReq_EST_Func_0023_activewarning2passive_acl,activewarning2passive_acl,est,stateflow/active2passive,test_actwrn2psv_acl.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置油门开度大于70% 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
101,test_SysReq_EST_Func_0023_activewarning2passive_AEB,activewarning2passive_AEB,est,stateflow/active2passive,test_actwrn2psv_AEB.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 激活AEB 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
102,test_SysReq_EST_Func_0023_activewarning2passive_brk,activewarning2passive_brk,est,stateflow/active2passive,test_actwrn2psv_brk.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置刹车压力大于10bar 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
103,test_SysReq_EST_Func_0023_activewarning2passive_CCO,activewarning2passive_CCO,est,stateflow/active2passive,test_actwrn2psv_CCO.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置特殊驾驶模式 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
104,test_SysReq_EST_Func_0023_activewarning2passive_clb,activewarning2passive_clb,est,stateflow/active2passive,test_actwrn2psv_clb.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置特殊驾驶模式 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
105,test_SysReq_EST_Func_0023_activewarning2passive_curv,activewarning2passive_curv,est,stateflow/active2passive,test_actwrn2psv_curv.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 进入曲率大于0.004的弯道 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
106,test_SysReq_EST_Func_0023_activewarning2passive_drvhan,activewarning2passive_drvhan,est,stateflow/active2passive,test_actwrn2psv_drvhan.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置驾驶员手力矩大于0.4Nm 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
107,test_SysReq_EST_Func_0023_activewarning2passive_drvmod3,activewarning2passive_drvmod3,est,stateflow/active2passive,test_actwrn2psv_drvmod3.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置特殊驾驶模式 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
108,test_SysReq_EST_Func_0023_activewarning2passive_ESP,activewarning2passive_ESP,est,stateflow/active2passive,test_actwrn2psv_ESP.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 激活ESP 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
109,test_SysReq_EST_Func_0023_activewarning2passive_ftgoff,activewarning2passive_ftgoff,est,stateflow/active2passive,test_actwrn2psv_ftgoff.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 关闭疲劳检测 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
110,test_SysReq_EST_Func_0023_activewarning2passive_gsft,activewarning2passive_gsft,est,stateflow/active2passive,test_actwrn2psv_gsft.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置上拨拨杆退出ACC 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
111,test_SysReq_EST_Func_0023_activewarning2passive_lanlos,activewarning2passive_lanlos,est,stateflow/active2passive,test_actwrn2psv_lanlos.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置车道线探测丢失 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
112,test_SysReq_EST_Func_0023_activewarning2passive_spd140,activewarning2passive_spd140,est,stateflow/active2passive,test_actwrn2psv_spd140.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置速度大于135kph 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
113,test_SysReq_EST_Func_0023_activewarning2passive_trnsp,activewarning2passive_trnsp,est,stateflow/active2passive,test_actwrn2psv_trnsp.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 设置特殊驾驶模式 判断EST是否处于passive状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
114,test_SysReq_EST_Func_0035_activestop2standstill,activestop2standstill,est,stateflow/active2standstill,test_actstp2stdstill.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 设置车辆静止 判断EST是否处于standstill状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
115,test_SysReq_EST_Func_0023_passive2fail,passive2fail,est,stateflow/fail2passive,test_fail2psv.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态
7. 取消注入故障 判断EST是否处于passive状态
8. 停止录制数据
9. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
116,test_SysReq_SysReq_EST_Func_0019_off2passive,off2passive,est,stateflow/off2passive,test_off2passive.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 设置自车车速25kph
3. 配置项目工程路径
4. 配置需要绘图信号 录制cve数据
5. 关闭EST功能 判断EST是否处于off状态
6. 打开EST功能 判断EST是否处于passive状态
7. 停止录制数据
8. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
117,test_SysReq_EST_Func_0020_activedeceleration2off,activedeceleration2off,est,stateflow/on2off,test_actdec2off.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 关闭EST功能 判断EST是否处于off状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
118,test_SysReq_EST_Func_0020_activeemergencelanechange2off,activeemergencelanechange2off,est,stateflow/on2off,test_actELC2off.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 关闭EST功能 判断EST是否处于off状态
13. 停止录制数据
14. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
119,test_SysReq_EST_Func_0020_activerightlanechange2off,activerightlanechange2off,est,stateflow/on2off,test_actRLC2off.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 关闭EST功能 判断EST是否处于off状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
120,test_SysReq_EST_Func_0020_activespeedadaption2off,activespeedadaption2off,est,stateflow/on2off,test_actspdadp2off.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 关闭EST功能 判断EST是否处于off状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
121,test_SysReq_EST_Func_0020_activestop2off,activestop2off,est,stateflow/on2off,test_actstp2off.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 关闭EST功能 判断EST是否处于off状态
10. 停止录制数据
11. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
122,test_SysReq_EST_Func_0020_activewarning2off,activewarning2off,est,stateflow/on2off,test_actwrn2off.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 关闭EST功能 判断EST是否处于off状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
123,test_SysReq_EST_Func_0020_passive2off,passive2off,est,stateflow/on2off,test_passive2off.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 设置自车车速25kph
3. 配置项目工程路径
4. 配置需要绘图信号 录制cve数据
5. 打开EST功能 判断EST是否处于passive状态
6. 关闭EST功能 判断EST是否处于off状态
7. 停止录制数据
8. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
124,test_SysReq_EST_Func_0035_standstill2off,standstill2off,est,stateflow/on2off,test_stdstl2off.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activestop状态 设置车辆静止
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于standstill状态
10. 关闭EST功能 判断EST是否处于off状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
125,test_SysReq_EST_Func_0024_passive2activewarning,passive2activewarning,est,stateflow/passive2actve_warning,test_psv2actwrn.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 判断EST是否处于passive状态
7. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
8. 判断EST是否处于activewarning状态
9. 停止录制数据
10. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
126,test_SysReq_EST_Func_0035_standstill2passive,standstill2passive,est,stateflow/standstill2passive,test_stdstl2passive.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activestop状态 设置车辆静止
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于standstill状态
10. 设置车辆非静止 判断EST是否处于passive状态
11. 停止录制数据
12. 台架状态复位",验证EST状态转换正确,HIL仿真环境,
127,test_SysReq_EST_Func_0023_activedeceleration2fail,activedeceleration2fail,est,stateflow/state2failure,test_actdec2fail.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activedeceleration状态
9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态
10. 停止录制数据
11. 取消注入故障 台架状态复位",验证EST状态转换正确,HIL仿真环境,
128,test_SysReq_EST_Func_0023_activeemergencelanechange2fail,activeemergencelanechange2fail,est,stateflow/state2failure,test_actELC2fail.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 判断EST是否处于activerightlanechange状态
8. 判断EST是否处于activedeceleration状态 设置速度显示大于20kph
9. 配置项目工程路径
10. 配置需要绘图信号 录制cve数据
11. 判断EST是否处于activeemergencelanechange状态
12. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态
13. 停止录制数据
14. 取消注入故障 台架状态复位",验证EST状态转换正确,HIL仿真环境,
129,test_SysReq_EST_Func_0023_activerightlanechange2fail,activerightlanechange2fail,est,stateflow/state2failure,test_actRLC2fail.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 判断EST是否处于activespeedadaption状态 设置速度显示大于20kph
7. 配置项目工程路径
8. 配置需要绘图信号 录制cve数据
9. 判断EST是否处于activerightlanechange状态
10. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态
11. 停止录制数据
12. 取消注入故障 台架状态复位",验证EST状态转换正确,HIL仿真环境,
130,test_SysReq_EST_Func_0023_activespeedadaption2fail,activespeedadaption2fail,est,stateflow/state2failure,test_actspdadp2fail.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activespeedadaption状态
9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态
10. 停止录制数据
11. 取消注入故障 台架状态复位",验证EST状态转换正确,HIL仿真环境,
131,test_SysReq_EST_Func_0023_activestop2fail,activestop2fail,est,stateflow/state2failure,test_actstp2fail.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 判断EST是否处于activewarning状态
6. 配置项目工程路径
7. 配置需要绘图信号 录制cve数据
8. 判断EST是否处于activestop状态
9. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态
10. 停止录制数据
11. 取消注入故障 台架状态复位",验证EST状态转换正确,HIL仿真环境,
132,test_SysReq_EST_Func_0023_activewarning2fail,activewarning2fail,est,stateflow/state2failure,test_actwrn2fail.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 激活ACC 判断ACC是否处于active状态 设置驾驶员重度疲劳
5. 配置项目工程路径
6. 配置需要绘图信号 录制cve数据
7. 判断EST是否处于activewarning状态
8. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态
9. 停止录制数据
10. 取消注入故障 台架状态复位",验证EST状态转换正确,HIL仿真环境,
133,test_SysReq_EST_Func_0023_passive2fail,passive2fail,est,stateflow/state2failure,test_psv2fail.py,HIL台架初始化且无故障情况,"1. HIL台架初始化且无故障情况 场景选择直道三车道
2. 打开EST功能
3. 设置自车车速25kph
4. 配置项目工程路径
5. 配置需要绘图信号 录制cve数据
6. 判断EST是否处于passive状态
7. 注入故障0x600016-蓄电池供电电压低于工作阈值 判断EST是否处于failure状态
8. 停止录制数据
9. 取消注入故障 台架状态复位",验证EST状态转换正确,HIL仿真环境,
